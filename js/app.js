let order = {};

$(function () {
	const data_iphone = [
		{
			id: "ip17pm",
			name: "iPhone 17 Pro Max",
			color: [
				{ id: "corange", name: "Cosmic Orange" },
				{ id: "silver", name: "<PERSON>" },
				{ id: "deep-blue", name: "Deep Blue" },
			],
			options: [
				{
					capacity: "256GB",
					sale: "37,999,000đ",
				},
				{
					capacity: "512GB",
					sale: "44,490,000đ",
				},
				{
					capacity: "1TB",
					sale: "50,999,000đ",
				},
				{
					capacity: "2TB",
					sale: "63,999,000đ",
				},
			],
		},
		{
			id: "ip17p",
			name: "iPhone 17 Pro",
			color: [
				{ id: "corange", name: "Cosmic Orange" },
				{ id: "silver", name: "Silver" },
				{ id: "deep-blue", name: "Deep Blue" },
			],
			options: [
				{
					capacity: "256GB",
					sale: "34,999,000đ",
				},
				{
					capacity: "512GB",
					sale: "41,499,000đ",
				},
				{
					capacity: "1TB",
					sale: "47,999,000đ",
				},
			],
		},
		{
			id: "ip17",
			name: "iPhone 17",
			color: [
				{ id: "black", name: "Black" },
				{ id: "white", name: "<PERSON>" },
				{ id: "mist-blue", name: "Mist Blue" },
				{ id: "lavender", name: "Lavender" },
				{ id: "sage", name: "Sage" },
			],
			options: [
				{
					capacity: "256GB",
					sale: "24,999,000đ",
				},
				{
					capacity: "512GB",
					sale: "31,499,000đ",
				},
			],
		},
		{
			id: "ip17air",
			name: "iPhone 17 Air",
			color: [
				{ id: "space-black", name: "Space Black" },
				{ id: "white", name: "White" },
				{ id: "light-gold", name: "Light Gold" },
				{ id: "sky-blue", name: "Sky Blue" },
			],
			options: [
				{
					capacity: "256GB",
					sale: "31,999,000đ",
				},
				{
					capacity: "512GB",
					sale: "38,499,000đ",
				},
				{
					capacity: "1Tb",
					sale: "44,999,000đ",
				},
			],
		},
	];

	$("#desktop .prices").slick({
		infinite: true,
		slidesToShow: 5,
		slidesToScroll: 5,
		dots: true,
		arrows: true,
		autoplay: true,
		autoplaySpeed: 2000,
		prevArrow:
			'<button class="slick-prev slick-arrow" aria-label="Previous" type="button" style=""><img src="assets/images/desktop/arrow-prev.png" alt="slick-prev"/></button>',
		nextArrow:
			'<button class="slick-next slick-arrow" aria-label="Next" type="button" style=""><img src="assets/images/desktop/arrow-next.png" alt="slick-next"/></button>',
		responsive: [
			{
				breakpoint: 1199,
				settings: {
					slidesToShow: 4,
					slidesToScroll: 4,
					arrows: false,
				},
			},
			{
				breakpoint: 991,
				settings: {
					slidesToShow: 3,
					slidesToScroll: 3,
				},
			},
			{
				breakpoint: 767,
				settings: {
					slidesToShow: 2,
					slidesToScroll: 2,
				},
			},
		],
	});

	$("#desktop .content-slider").slick({
		slidesToShow: 1,
		slidesToScroll: 1,
		arrows: false,
		fade: true,
		asNavFor: "#desktop .nav-slider",
	});

	$("#desktop .nav-slider").slick({
		slidesToShow: 4,
		slidesToScroll: 1,
		asNavFor: "#desktop .content-slider",
		dots: false,
		centerMode: true,
		focusOnSelect: true,
	});

	$("#mobile .content-slider").slick({
		slidesToShow: 1,
		slidesToScroll: 1,
		arrows: false,
		fade: true,
		asNavFor: "#mobile .nav-slider",
	});

	$("#mobile .nav-slider").slick({
		slidesToShow: 6,
		slidesToScroll: 1,
		asNavFor: "#mobile .content-slider",
		dots: false,
		centerMode: true,
		focusOnSelect: true,
	});

	$.each(data_iphone, function (i, item) {
		const iphoneEle = $(".container.iphone");
		const selected = i == 0 ? "selected" : "";
		iphoneEle.find(".product-type").append(templateType(item.id, item.name, selected));
		if (i == 0) {
			iphoneEle.find(".product-title").text(item.name);
			iphoneEle
				.find(".product-color .colors")
				.append(templateColors(item.id, item.color, iphoneEle.find(".product-img .img img")));
			iphoneEle.find(".product-options").append(templateOptions(item.options));
		}
	});


	$('nav .list-menu .list-item').on('click', function () {
		let parent_wrapper = $(this).closest('.parent');
		let section = $(this).data('scroll-to');
		$('html, body').animate({
			scrollTop: $(parent_wrapper).find(section).offset().top,
		}, 1000);
	});

	$('.bang-gia .btn-scroll-to').on('click', function () {
		let parent_wrapper = $(this).closest('.parent');
		$('html, body').animate({
			scrollTop: $(parent_wrapper).find('.preorder-section.iphone').offset().top,
		}, 1000);
	});

	$(document).on("click", ".preorder-section .product-type .type", function () {
		const id = $(this).data("type");
		const ele = $(this).closest(".preorder-section");
		let obj = null;
		if (ele.data("type") == "iphone") {
			obj = data_iphone.find((item) => item.id == id);
		} else {
			obj = data_ipad.find((item) => item.id == id);
		}

		ele.find(".product-type .type").removeClass("selected");
		$(this).addClass("selected");
		ele.find(".product-color .colors").empty();
		ele.find(".product-options").empty();
		ele.find(".product-title").text(obj.name);
		ele.find(".product-color .colors").append(templateColors(obj.id, obj.color, ele.find(".product-img .img img")));
		ele.find(".product-options").append(templateOptions(obj.options));
	});

	$(document).on("click", ".preorder-section .product-color .slt-color", function () {
		const ele = $(this).closest(".preorder-section");

		ele.find(".product-color .slt-color").removeClass("selected");
		$(this).addClass("selected");

		ele.find(".product-img .img img").attr("src", $(this).data("image-src"));
	});

	$(document).on("click", ".preorder-section .product-options .option", function () {
		const ele = $(this).closest(".preorder-section");

		ele.find(".product-options .option").removeClass("selected");
		$(this).addClass("selected");
	});

	$(document).on("click", ".preorder-section .btn-select", function () {
		const ele = $(this).closest(".preorder-section"),
			type = ele.find(".product-type .type.selected").text(),
			capacity = ele.find(".product-options .option.selected").data("option"),
			price = ele.find(".product-options .option.selected").data("price"),
			color = ele.find(".product-color .slt-color.selected").data("color");
		img = ele.find(".product-color .slt-color.selected").data("image-src");
		let km = "",
			data = { type, capacity, price, color, img };
		if (ele.data("type") == "iphone") {
			km = ele.find(".khuyen-mai input[type=radio]:checked").val();
		}
		const id = Date.now();
		order[id] = { type, capacity, color, km };
		renderPicked(id, data, ele.find(".product-picked .picked"));
		$(this).closest(".parent").find(".preorder-form .list-product").append(templatePickedForm(id, data));
	});

	$(document).on("click", ".gifts .capacity .decrease", function () {
		const ele = $(this).closest(".box");
		const numb = ele.find(".number");
		const color = $(this).closest(".list-item").find('.selector-color .slt-color.selected').data('color');
		const quantity = parseInt(numb.text()) - 1;
		const id = ele.attr("data-id");
		if (quantity == 0) {
			numb.text(0);
			delete order[id];
			ele.attr('data-id', '');
			$(this).closest(".parent").find(".preorder-form .list-product .product[data-id='" + id + "']").remove();
		} else {
			numb.text(quantity);
			order[id].quantity = quantity;
			order[id].color = color;
			$(this).closest(".parent")
				.find(".preorder-form .list-product .product[data-id='" + id + "'] .quantity span")
				.text(quantity);
			$(this).closest(".parent")
				.find(".preorder-form .list-product .product[data-id='" + id + "'] .option span")
				.text(color);
		}
	});

	$(document).on("click", ".gifts .capacity .increase", function () {
		const ele = $(this).closest(".box");
		const numb = ele.find(".number");
		const type = ele.data("type");
		const img = ele.data("img");
		let color = $(this).closest(".list-item").find('.selector-color .slt-color.selected').data('color');
		if (color == undefined) {
			color = '';
		}
		const quantity = parseInt(numb.text()) + 1;
		if (quantity == 1) {
			let data = { img, type, quantity, color };
			let id = Date.now();
			ele.attr("data-id", id);
			$(this).closest(".parent").find(".preorder-form .list-product").append(templatePickedForm(id, data));
			order[id] = { type, quantity, color };
		} else {
			let id = ele.attr("data-id");
			order[id].quantity = quantity;
			order[id].color = color;
			$(this).closest(".parent")
				.find(".preorder-form .list-product .product[data-id='" + id + "'] .quantity span")
				.text(quantity);
			$(this).closest(".parent")
				.find(".preorder-form .list-product .product[data-id='" + id + "'] .option span")
				.text(color);
		}
		numb.text(quantity);
	});

	$(document).on("click", ".gifts .selector-color .slt-color", function () {
		$(this).closest(".selector-color").find(".slt-color").removeClass("selected");
		$(this).addClass("selected");
	});

	$(document).on("click", ".preorder-section .product-picked .delete", function () {
		const id = $(this).data("id");
		delete order[id];
		$(this)
			.closest(".parent")
			.find(".preorder-form .list-product .product[data-id='" + id + "']")
			.remove();
		$(this).closest(".picked-box-wrapper").remove();
	});

	$('.checkmark').on('click', function () {
		$(this).closest('.form-check').find('input').prop('checked', true).change();
	})

	$(".preorder-form form").submit(function (e) {
		e.preventDefault();
		if (Object.keys(order).length > 0) {
			let data = $(this).serializeArray();
			let products = [];
			$.each(order, function (i, v) {
				products.push(v);
			});
			data.push({ name: 'choose_product', value: JSON.stringify(products) });
			sendData(data, $(this));
		} else {
			alert('Bạn chưa chọn sản phẩm nào');
		}
	});
});

function sendData(data, eleForm) {
	$.ajax({
		type: "POST",
		data: data,
		url: "process.php",
		beforeSend: function () {
			eleForm.find('button[type=submit]').prop("disabled", true);
		},
		success: function (resp) {
			if (resp.success) {
				console.log(resp);
				alert("Đã đăng ký thành công.");
				location.reload();
			} else {
				eleForm.find('button[type=submit]').prop("disabled", false);
				alert("Có lỗi xảy ra");
			}
		},
	});
}

function templateType(id, name, selected) {
	let html = '<div class="type ' + selected + '" data-type="' + id + '">' + name + "</div>";
	return html;
}

function templateColors(id_type, colors, eleImg) {
	let html = "";

	$.each(colors, function (i, item) {
		const id = item.id;
		const name = item.name;
		const selected = i == 0 ? "selected" : "";
		const src = "assets/images/product/" + id_type + "-" + id + ".png";
		if (selected) {
			eleImg.attr("src", src);
		}
		html +=
			'<label class="slt-color ' +
			id +
			" " +
			selected +
			'" data-image-src="' +
			src +
			'" data-color="' +
			name +
			'"><span></span></label>';
	});

	return html;
}

function templateOptions(options) {
	let html = "";

	$.each(options, function (i, item) {
		const selected = i == 0 ? "selected" : "";
		html +=
			'<div class="option ' +
			selected +
			'" data-option="' +
			item.capacity +
			'" data-price="' +
			item.sale +
			'">' +
			'<p class="capacity">' +
			item.capacity +
			"</p>" +
			'<p class="sale">' +
			item.sale +
			"</p>" +
			"</div>";
	});

	return html;
}

function renderPicked(id, data, ele) {
	let html =
		'<div class="picked-box-wrapper"><div class="picked-box type">' +
		data.type +
		"</div>" +
		'<div class="picked-box option"><p class="capacity">' +
		data.capacity +
		'</p><p class="price">' +
		data.price +
		"</p></div>" +
		'<div class="picked-box color">' +
		data.color +
		"</div>" +
		'<div class="delete" data-id="' +
		id +
		'"><img src="assets/images/delete.png" alt="">Xóa</div></div>' +
		"</div>";
	ele.append(html);
}

function templatePickedForm(id, params) {
	let html = '<div class="row product" data-id="' + id + '">' +
		'<div class="col-2 img-product">' +
		'<img class="img-fluid" src="' + params.img +
		'" alt="" />' +
		"</div>" +
		'<div class="col-10 info-product">' +
		'<p class="type">' +
		params.type +
		"</p>";
	if (params.capacity && params.color) {
		html +=
			'<p class="option">Dung lượng: <span>' +
			params.capacity +
			"</span> - màu sắc: <span>" +
			params.color +
			"</span></p>";
	}
	if (params.color) {
		html += '<p class="option">Màu sắc: <span>' + params.color + '</span></p>';
	}
	if (params.quantity) {
		html += '<p class="quantity">số lượng: <span>' + params.quantity + "</span></p>";
	}

	html += "</div></div>";

	return html;
}
