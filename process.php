<?php

require_once('vendor/autoload.php');

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

header('Content-type: application/json');
$ini = parse_ini_file('app.ini');
date_default_timezone_set($ini['app_timezone']);
// instantiate the library
$db = new Zebra_Database();
// connect to a server and select a database
$db->connect($ini['db_host'], $ini['db_user'], $ini['db_password'], $ini['db_name']);
$db->set_charset('utf8', 'utf8_general_ci');
$memberName = $_POST['client_name'];
$memberPhone = $_POST['client_phone'];
$memberEmail = $_POST['client_email'] ?? null;
$memberAddress = $_POST['client_address'] ?? null;
$miStore = $_POST['mi_store'] ?? null;
$product = $_POST['choose_product'];
$quantity = $_POST['mi_quantity'] ?? null;
$color = $_POST['mi_color'] ?? null;
$note = $_POST['note'] ?? null;

if ($memberName != '' && $memberPhone != '') {
    $db->insert(
        'members',
        [
            'name'         => $memberName,
            'phone'        => $memberPhone,
            'email'        => $memberEmail,
            'address'      => $memberAddress,
            'store'        => $miStore,
            'product'      => $product,
            'quantity'     => $quantity,
            'color'        => $color,
            'is_sent_mail' => 0,
            'note'         => $note,
            'created_at'   => date('Y-m-d H:i:s'),
        ]
    );
    $memberId = $db->insert_id();

    if ($memberId > 0) {
        echo ($memberEmail && $ini['is_sent_mail'])
            ? sendMail($memberId, $memberName, $memberEmail)
            : json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false]);
    }

    return true;
}
function siteURL()
{
    $protocol = ((!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] != 'off') ||
        $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $domainName = $_SERVER['HTTP_HOST'];
    return $protocol.$domainName;
}

function sendMail($memberId, $memberName, $memberEmail)
{
    $ini = parse_ini_file('app.ini');
    $db = new Zebra_Database();
    $db->connect($ini['db_host'], $ini['db_user'], $ini['db_password'], $ini['db_name']);
    $db->set_charset('utf8', 'utf8_general_ci');
    $emailTemplate = file_get_contents("./mails/email.html");
    $emailTemplate = str_replace("*|MAIL_TITLE|*", 'DStore - Đăng ký nhận thông tin thành công', $emailTemplate);
    $emailTemplate = str_replace("*|MAIL_PREHEADER|*", 'Đăng ký nhận thông tin thành công', $emailTemplate);
    $emailTemplate = str_replace("*|CUSTOMER_NAME|*", $memberName, $emailTemplate);
    $emailTemplate = str_replace("*|SRC_IMAGE|*", siteURL().'/assets/images/email240921.jpg', $emailTemplate);

    $mail = new PHPMailer(true);

    try {
        //Server settings
        $mail->isSMTP(); // Send using SMTP
        $mail->CharSet = 'UTF-8';
        $mail->Host = $ini['mail_host']; // Set the SMTP server to send through
        $mail->Port = $ini['mail_port']; // TCP port to connect to, use 465 for `PHPMailer::ENCRYPTION_SMTPS` above

        if ($ini['mail_username']) {
            $mail->Username = $ini['mail_username']; // SMTP username
        }
        if ($ini['mail_password']) {
            $mail->Password = $ini['mail_password']; // SMTP password
        }

        $mail->SMTPAuth = true; // Enable SMTP authentication
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // Enable TLS encryption; `PHPMailer::ENCRYPTION_SMTPS` encouraged
//        $mail->SMTPDebug = SMTP::DEBUG_SERVER; // Enable verbose debug output

        //Recipients
        $mail->setFrom('<EMAIL>', 'Chăm sóc khách hàng 1Digital');
        $mail->addAddress($memberEmail, $memberName);     // Add a recipient

        // $mail->addReplyTo('<EMAIL>', 'Information');
        // $mail->addCC('<EMAIL>');
        // $mail->addBCC('<EMAIL>');


        // Content
        $mail->isHTML(true); // Set email format to HTML
        $mail->Subject = '[ĐĂNG KÝ NHẬN THÔNG TIN THÀNH CÔNG] - SIÊU PHẨM APPLE 2021 - 1DIGITAL';
        $mail->Body = $emailTemplate;
        $mail->send();
        $db->update('members', ['is_sent_mail' => 1], 'id = '.$memberId);
        return json_encode([
            'success' => true,
            'data'    => $db->dcount('id', 'members')
        ]);
    } catch (Exception $e) {
        return json_encode([
            'success' => false,
            'Message' => "Message could not be sent. Mailer Error: {$mail->ErrorInfo}"
        ]);
    }
}

function genarateSku($brand,$storage,$color)
{

}
