/* Modal popup*/
$main-color-modal: #0070D1;

.close-wrapper {
  max-width: 28px;
  max-height: 28px;
  position: absolute;
  top: 10px;
  right: 13px;
  cursor: pointer;

  &:hover {
    opacity: .8;
  }
}

.modal {
  .modal-content {
    background: #FFFFFF;
    box-shadow: 0 6px 19px rgba(0, 0, 0, 0.25);
    border-radius: 15px;
  }

  .modal-body {
    padding: 25px 24px 40px;
  }

  .product {
    .header {
      position: relative;
      margin-top: 27px;
      margin-bottom: 27px;
      cursor: pointer;

      &.active {
        .btn-collapse {
          img {
            transform: rotate(180deg);
          }
        }
      }

      .title {
        padding: 0 15px;

        p {
          font-weight: bold;
          font-size: 22px;
          line-height: 107.7%;
          letter-spacing: 0.01em;
          color: #3E3E3E;
          margin-bottom: 0;

          &:last-child {
            font-size: 45px;
            line-height: 113.2%;
          }
        }
      }

      .btn-collapse {
        position: absolute;
        top: 16px;
        right: 48px;

        img {
          max-width: 38px;
          object-fit: contain;
        }


      }
    }

    .content {
      padding: 23px 0 0;

      .general {
        padding-right: 30px;
        padding-left: 30px;

        .summary {
          margin-bottom: 20px;

          .img-product {
            img {
              max-height: 270px;
              object-fit: contain;
            }
          }

          .products {
            .list {
              overflow: auto;
              max-height: 220px;
              height: 100%;
            }

            .info {
              font-weight: 600;
              font-size: 20px;
              line-height: 27px;
              color: #000000;
            }

            .picked {
              display: flex;
              align-items: stretch;
              margin-bottom: 22px;

              &:last-child {
                margin-bottom: 0;
              }

              .type {
                display: flex;
                justify-content: center;
                align-items: center;
                background: #005592;
                box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
                border-radius: 10px;
                font-weight: 600;
                font-size: 13px;
                line-height: 18px;
                text-align: center;
                text-transform: capitalize;
                color: #FFFFFF;
                padding: 8px;
                margin-right: 10px;
              }

              .capacity {
                display: flex;
                justify-content: center;
                align-items: center;
                background: #FFFFFF;
                border: 1.5px solid #0086E6;
                box-sizing: border-box;
                box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
                border-radius: 10px;
                font-weight: bold;
                font-size: 19px;
                line-height: 26px;
                text-align: center;
                text-transform: uppercase;
                color: #005592;
                padding: 5px 10px;
                margin-right: 10px;
              }

              .color {
                display: flex;
                justify-content: center;
                align-items: center;
                background: #FFFFFF;
                border: 1.5px solid #0086E6;
                box-sizing: border-box;
                box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
                border-radius: 10px;
                font-weight: 600;
                font-size: 16px;
                line-height: 22px;
                text-align: center;
                text-transform: capitalize;
                color: #005592;
                padding: 5px 10px;
                margin-right: 20px;
              }

              .delete {
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                font-size: 13px;
                line-height: 18px;
                text-align: center;
                text-decoration-line: underline;
                text-transform: capitalize;
                color: #C72C2C;

                img {
                  margin-right: 5px;
                  max-width: 14px;
                  object-fit: contain;
                }
              }
            }
          }
        }

        .step {
          margin-bottom: 32px;

          .step-title {
            font-weight: 600;
            font-size: 20px;
            line-height: 27px;
            text-transform: capitalize;
            color: #000000;
            margin-bottom: 15px;
          }

          .product-type {
            .form-check-inline {
              background: #FFFFFF;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
              border-radius: 15px;

              label {
                font-weight: 600;
                font-size: 16px;
                line-height: 22px;
                text-align: center;
                text-transform: capitalize;
                color: #000000;
                margin-bottom: 0;
                padding: 12px 20px;
                cursor: pointer;
                width: 100%;
              }

              &.active {
                background: #005592;

                label {
                  color: #FFFFFF;
                }
              }
            }
          }

          .product-capacity {
            .form-check-inline {
              background: #FFFFFF;
              box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
              border-radius: 18px;

              label {
                font-weight: bold;
                font-size: 20px;
                line-height: 27px;
                text-align: center;
                text-transform: uppercase;
                color: #494949;
                margin-bottom: 0;
                padding: 12px 20px;
                cursor: pointer;
                width: 100%;
              }

              &.active {
                border: 1.5px solid #0086E6;

                label {
                  color: #005592;
                }
              }
            }
          }

          .product-color {
            display: flex;

            label {
              border-radius: 50px;
              box-sizing: border-box;
              padding: 5px;
              margin-bottom: 0;
              border: 1px solid transparent;
              cursor: pointer;

              span {
                display: block;
                width: 24px;
                height: 24px;
                border-radius: 50px;
              }

              &.grey {
                span {
                  background-color: #808080;
                }
              }

              &.gold {
                span {
                  background-color: #DAA520;
                }
              }

              &.silver {
                span {
                  background-color: #C0C0C0;
                }
              }

              &.blue {
                span {
                  background-color: #0000FF;
                }
              }

              &.pink {
                span {
                  background-color: #FFC0CB;
                }
              }

              &.black {
                span {
                  background-color: #000000;
                }
              }

              &.white {
                span {
                  background-color: #FFFFFF;
                  box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.25);
                }
              }

              &.red {
                span {
                  background-color: #FF0000;
                }
              }

              &.purple {
                span {
                  background-color: #9F64FF;
                }
              }
            }

            .form-check-inline {
              display: inline-block;
              text-align: center;
              margin-right: 50px;

              &:last-child {
                margin-right: 0;
              }

              &.active {
                label {
                  border: 1px solid #000000;
                }
              }
            }

            p {
              font-size: 20px;
              line-height: 27px;
              text-align: center;
              text-transform: capitalize;
              color: #393939;
              margin-bottom: 0;
            }
          }
        }

        .action {
          margin-bottom: 36px;
          text-align: center;

          .btn-more {
            background: #FFFFFF;
            border: 1.5px solid #005592;
            box-sizing: border-box;
            border-radius: 11px;
            font-weight: bold;
            font-size: 19px;
            line-height: 107.7%;
            text-align: center;
            text-transform: capitalize;
            color: #0071C2;
            width: 80%;
            padding: 16px 0;
            max-width: 534px;
          }
        }
      }
    }
  }

  #client-info {
    margin-top: 28px;

    & > p {
      font-weight: bold;
      font-size: 26px;
      line-height: 35px;
      text-transform: capitalize;
      color: #000000;
      margin-bottom: 12px;
    }

    .form-client {
      input {
        background: #FFFFFF;
        border: 1px solid #D0D0D0;
        box-sizing: border-box;
        border-radius: 11px;
        font-size: 20px;
        line-height: 27px;
        color: #7A7A7A;
        padding: 26px 20px;
        margin-bottom: 14px;
      }

      button[type=submit] {
        padding: 16px 0;
        max-height: 54px;
        width: 100%;
        background: #C72C2C;
        border-radius: 11px;
        font-weight: bold;
        font-size: 20px;
        line-height: 107.7%;
        text-align: center;
        text-transform: capitalize;
        color: #FFFFFF;

      }
    }
  }

  #counter {
    margin-top: 34px;
    text-align: center;

    .dropdown-divider {
      margin-top: 34px;
      margin-bottom: 30px;
    }

    p {
      line-height: 180%;
      text-align: center;
      letter-spacing: 0.01em;
      text-transform: capitalize;
      color: #005592;
      margin-bottom: 0;

      &.text-1 {
        font-weight: 600;
        font-size: 35px;
      }

      &.text-2 {
        font-weight: normal;
        font-size: 30px;
      }
    }
  }
}

@media screen and (max-width: 1199px) {
  .close-wrapper {
    max-width: 25px;
    max-height: 25px;
  }
  .modal {
    .modal-dialog {
      max-width: 90%;
    }

    .modal-body {
      padding: 39px 15px 41px;
    }

    .product {
      .header {
        .title {
          p {
            font-size: 18px;

            &:last-child {
              font-size: 34px;
            }
          }
        }

        .btn-collapse {
          img {
            max-width: 35px
          }
        }
      }

      .content {
        .general {
          .summary {
            .products {
              .list {
                max-height: 180px;
              }

              .picked {
                .type {
                  font-size: 14px;
                  line-height: normal;
                  padding: 7px;
                }

                .capacity {
                  font-size: 14px;
                  line-height: normal;
                  padding: 7px;
                }

                .color {
                  font-size: 16px;
                  line-height: 22px;
                  padding: 7px;
                }
              }
            }
          }

          .step {
            .step-title {
              font-size: 14px;
            }

            .product-type {
              .form-check-inline {
                label {
                  font-size: 16px;
                  line-height: 24px;
                }
              }
            }

            .product-capacity {
              .form-check-inline {
                label {
                  font-size: 16px;
                  line-height: 24px;
                }
              }
            }

            .product-color {
              .form-check-inline {
                margin-right: 45px;
              }

              label {
                padding: 4px;

                span {
                  width: 21px;
                  height: 21px;
                }
              }

              p {
                font-size: 18px;
                line-height: 22px;
              }
            }
          }

          .action {
            .btn {
              &.btn-more {
                font-size: 20px;
              }

              &.btn-confirm {
                font-size: 20px;
              }
            }
          }
        }
      }
    }

    #client-info {
      margin-top: 18px;

      & > p {
        font-size: 20px;
        line-height: 25px;
        margin-bottom: 1rem;
      }

      .form-client {
        input {
          font-size: 14px;
          padding: 21px 0 21px 25px;
        }

        button[type=submit] {
          font-size: 18px;
          padding: 12px 0;
          max-height: 44px;
        }
      }
    }

    #counter {
      margin-top: 24px;

      .dropdown-divider {
        margin-top: 24px;
        margin-bottom: 20px;
      }

      p {
        &.text-1 {
          font-size: 30px;
        }

        &.text-2 {
          font-size: 26px;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .modal {
    .product {
      .content {
        .general {
          .step {
            .product-type {
              display: flex;
              justify-content: center;
              align-items: center;
              flex-wrap: wrap;

              .form-check-inline {
                flex: 0 0 calc(50% - .75rem);
                margin-bottom: 14px;
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 767px) {
  .modal .modal-dialog {
    max-width: initial;
  }
  .modal .modal-body > .info {
    font-size: 17px;
    line-height: 151.18%;
  }

  .close-wrapper {
    max-width: 21px;
    max-height: 21px;
    top: 10px;
    right: 11px;
  }
  .modal .product .header {
    margin-top: 16px;
    margin-bottom: 19px;
  }
  .modal .product .header .title p {
    font-size: 13px;
    line-height: 107.7%;
  }
  .modal .product .header .title p:last-child {
    font-size: 28px;
    line-height: 113.2%;
  }
  .modal .product .header .btn-collapse {
    top: 10px;
    right: 20px;
  }
  .modal .product .header .btn-collapse img {
    max-width: 20px;
  }

  .modal .product .content .general .step {
    margin-bottom: 26px;
  }
  .modal .product .content .general .step .col-12 {
    padding-left: 0;
    padding-right: 0;
  }
  .modal .product .content .general .step .step-title {
    font-size: 14px;
    line-height: 19px;
  }
  .modal .product .content .general .step .product-type {
    align-items: stretch;
  }
  .modal .product .content .general .step .product-type .form-check-inline {
    justify-content: center;
  }
  .modal .product .content .general .step .product-type .form-check-inline label {
    font-size: 15px;
    line-height: 20px;
    padding: 10px 15px;
  }
  .modal .product .content .general .step .product-capacity .form-check-inline.active label {
    font-size: 14px;
    line-height: 19px;
    padding: 10px 15px;
  }

  .modal .product .content .general .step .product-color {
    justify-content: space-evenly;
  }
  .modal .product .content .general .step .product-color .form-check-inline {
    flex: 1;
    justify-content: center;
    margin-right: 0;
  }
  .modal .product .content .general .step .product-color p {
    font-size: 14px;
    line-height: 19px;
  }
  .modal .product .content .general .action .col-6 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0;
  }
  .modal .product .content .general .action .btn.btn-more {
    font-size: 14px;
    line-height: 107.7%;
    margin-bottom: 12px;
  }
  .modal .product .content .general .action .btn.btn-confirm {
    font-size: 14px;
    line-height: 107.7%;
  }
  .modal .product .content .general .summary .products {
    margin-top: 31px;
    padding: 0;
  }
  .modal .product .content .general .summary .products .picked .type,
  .modal .product .content .general .summary .products .picked .capacity,
  .modal .product .content .general .summary .products .picked .color {
    font-size: 12px;
    line-height: 16px;
  }
  .modal #client-info {
    margin-top: 12px;
  }
  .modal #client-info > p {
    font-size: 13px;
    line-height: 18px;
    margin-bottom: 13px;
  }
  .modal #client-info .form-client input {
    margin-bottom: 7px;
    font-size: 16px;
    line-height: 22px;
  }
  .modal #counter {
    margin-top: 14px;

    .dropdown-divider {
      margin-top: 14px;
      margin-bottom: 10px;
    }
  }
  .modal #counter p.text-1 {
    font-size: 25px;
    line-height: 166%;
  }
  .modal #counter p.text-2 {
    font-size: 21px;
    line-height: 166%;
  }
}
