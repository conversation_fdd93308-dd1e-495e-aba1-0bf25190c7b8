/*Phone button*/
#phone-fixed {
  position: fixed;
  bottom: 15px;
  z-index: 9999;
}

.digi-alo-phone {
  background-color: transparent;
  width: 80px;
  height: 80px;
  cursor: pointer;
  z-index: 200000 !important;
  -webkit-backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  -webkit-transition: visibility 0.5s;
  -moz-transition: visibility 0.5s;
  -o-transition: visibility 0.5s;
  transition: visibility 0.5s;
  right: 0;
  top: 0;
}

.digi-alo-ph-circle {
  width: 60px;
  height: 60px;
  top: 10px;
  left: 10px;
  position: absolute;
  background-color: transparent;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  border: 2px solid rgba(30, 30, 30, 0.4);
  opacity: 0.1;
  -webkit-animation: digi-alo-circle-anim 1.2s infinite ease-in-out;
  -moz-animation: digi-alo-circle-anim 1.2s infinite ease-in-out;
  -ms-animation: digi-alo-circle-anim 1.2s infinite ease-in-out;
  -o-animation: digi-alo-circle-anim 1.2s infinite ease-in-out;
  animation: digi-alo-circle-anim 1.2s infinite ease-in-out;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}

.digi-alo-ph-circle-fill {
  width: 50px;
  height: 50px;
  top: 15px;
  left: 15px;
  position: absolute;
  background-color: #000;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  border: 2px solid transparent;
  opacity: 0.1;
  -webkit-animation: digi-alo-circle-fill-anim 2.3s infinite ease-in-out;
  -moz-animation: digi-alo-circle-fill-anim 2.3s infinite ease-in-out;
  -ms-animation: digi-alo-circle-fill-anim 2.3s infinite ease-in-out;
  -o-animation: digi-alo-circle-fill-anim 2.3s infinite ease-in-out;
  animation: digi-alo-circle-fill-anim 2.3s infinite ease-in-out;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}

.digi-alo-ph-img-circle {
  width: 30px;
  height: 30px;
  top: 25px;
  left: 25px;
  position: absolute;
  background: rgba(30, 30, 30, 0.1);
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  border: 2px solid transparent;
  opacity: 0.7;
  -webkit-animation: digi-alo-circle-img-anim 1s infinite ease-in-out;
  -moz-animation: digi-alo-circle-img-anim 1s infinite ease-in-out;
  -ms-animation: digi-alo-circle-img-anim 1s infinite ease-in-out;
  -o-animation: digi-alo-circle-img-anim 1s infinite ease-in-out;
  animation: digi-alo-circle-img-anim 1s infinite ease-in-out;
}

.digi-alo-phone.digi-alo-green {
  .digi-alo-ph-circle-fill {
    background-color: rgba(0, 175, 242, 0.5);
    opacity: 0.75 !important;
  }

  .digi-alo-ph-img-circle {
    background-color: #00aff2;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      max-width: 30px;
      max-height: 30px;
    }
  }

  .digi-alo-ph-circle {
    border-color: #00aff2;
    opacity: 0.5;
  }

  &.digi-alo-hover, &:hover {
    .digi-alo-ph-circle {
      border-color: #75eb50;
      opacity: 0.5;
    }

    .digi-alo-ph-circle-fill {
      background-color: rgba(117, 235, 80, 0.5);
      opacity: 0.75 !important;
    }

    .digi-alo-ph-img-circle {
      background-color: #75eb50;
    }
  }
}
