@-moz-keyframes digi-alo-circle-anim {
    0% {
        transform: rotate(0) scale(0.5) skew(1deg);
        opacity: 0.1;
    }

    30% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.5;
    }

    100% {
        transform: rotate(0) scale(1) skew(1deg);
        opacity: 0.1;
    }
}

@-webkit-keyframes digi-alo-circle-anim {
    0% {
        transform: rotate(0) scale(0.5) skew(1deg);
        opacity: 0.1;
    }

    30% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.5;
    }

    100% {
        transform: rotate(0) scale(1) skew(1deg);
        opacity: 0.1;
    }
}

@-o-keyframes digi-alo-circle-anim {
    0% {
        transform: rotate(0) scale(0.5) skew(1deg);
        opacity: 0.1;
    }

    30% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.5;
    }

    100% {
        transform: rotate(0) scale(1) skew(1deg);
        opacity: 0.1;
    }
}

@keyframes digi-alo-circle-anim {
    0% {
        transform: rotate(0) scale(0.5) skew(1deg);
        opacity: 0.1;
    }

    30% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.5;
    }

    100% {
        transform: rotate(0) scale(1) skew(1deg);
        opacity: 0.1;
    }
}

@-moz-keyframes digi-alo-circle-fill-anim {
    0% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.2;
    }

    50% {
        transform: rotate(0) scale(1) skew(1deg);
        opacity: 0.2;
    }

    100% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.2;
    }
}

@-webkit-keyframes digi-alo-circle-fill-anim {
    0% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.2;
    }

    50% {
        transform: rotate(0) scale(1) skew(1deg);
        opacity: 0.2;
    }

    100% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.2;
    }
}

@-o-keyframes digi-alo-circle-fill-anim {
    0% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.2;
    }

    50% {
        transform: rotate(0) scale(1) skew(1deg);
        opacity: 0.2;
    }

    100% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.2;
    }
}

@keyframes digi-alo-circle-fill-anim {
    0% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.2;
    }

    50% {
        transform: rotate(0) scale(1) skew(1deg);
        opacity: 0.2;
    }

    100% {
        transform: rotate(0) scale(0.7) skew(1deg);
        opacity: 0.2;
    }
}

@-moz-keyframes digi-alo-circle-img-anim {
    0% {
        transform: rotate(0) scale(1) skew(1deg);
    }

    10% {
        transform: rotate(-25deg) scale(1) skew(1deg);
    }

    20% {
        transform: rotate(25deg) scale(1) skew(1deg);
    }

    30% {
        transform: rotate(-25deg) scale(1) skew(1deg);
    }

    40% {
        transform: rotate(25deg) scale(1) skew(1deg);
    }

    50% {
        transform: rotate(0) scale(1) skew(1deg);
    }

    100% {
        transform: rotate(0) scale(1) skew(1deg);
    }
}

@-webkit-keyframes digi-alo-circle-img-anim {
    0% {
        transform: rotate(0) scale(1) skew(1deg);
    }

    10% {
        transform: rotate(-25deg) scale(1) skew(1deg);
    }

    20% {
        transform: rotate(25deg) scale(1) skew(1deg);
    }

    30% {
        transform: rotate(-25deg) scale(1) skew(1deg);
    }

    40% {
        transform: rotate(25deg) scale(1) skew(1deg);
    }

    50% {
        transform: rotate(0) scale(1) skew(1deg);
    }

    100% {
        transform: rotate(0) scale(1) skew(1deg);
    }
}

@-o-keyframes digi-alo-circle-img-anim {
    0% {
        transform: rotate(0) scale(1) skew(1deg);
    }

    10% {
        transform: rotate(-25deg) scale(1) skew(1deg);
    }

    20% {
        transform: rotate(25deg) scale(1) skew(1deg);
    }

    30% {
        transform: rotate(-25deg) scale(1) skew(1deg);
    }

    40% {
        transform: rotate(25deg) scale(1) skew(1deg);
    }

    50% {
        transform: rotate(0) scale(1) skew(1deg);
    }

    100% {
        transform: rotate(0) scale(1) skew(1deg);
    }
}

@keyframes digi-alo-circle-img-anim {
    0% {
        transform: rotate(0) scale(1) skew(1deg);
    }

    10% {
        transform: rotate(-25deg) scale(1) skew(1deg);
    }

    20% {
        transform: rotate(25deg) scale(1) skew(1deg);
    }

    30% {
        transform: rotate(-25deg) scale(1) skew(1deg);
    }

    40% {
        transform: rotate(25deg) scale(1) skew(1deg);
    }

    50% {
        transform: rotate(0) scale(1) skew(1deg);
    }

    100% {
        transform: rotate(0) scale(1) skew(1deg);
    }
}

@-webkit-keyframes pulsate-fwd {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
    50% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1);
    }
    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

@keyframes pulsate-fwd {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
    50% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1);
    }
    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}