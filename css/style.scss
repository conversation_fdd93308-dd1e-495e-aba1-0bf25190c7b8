@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap");

body {
	font-family: "Open Sans", sans-serif;
}

.img-fluid {
	width: 100%;
}

.banner {
	cursor: pointer;
}

input {
	&[type="radio"] {
		display: none;
	}
}

.ip_type {
	text-align: center;

	.form-check-inline {
		box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
		border-radius: 18px;

		label {
			padding: 19px 21px;
			font-weight: 600;
			font-size: 22px;
			line-height: 30px;
			text-align: center;
			text-transform: capitalize;
			color: #000000;
			cursor: pointer;
			margin-bottom: 0;
		}

		&.active {
			background: #005592;

			label {
				color: #ffffff;
			}
		}
	}
}

.ip_capacity {
	text-align: center;

	.form-check-inline {
		background: #ffffff;
		box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
		border-radius: 18px;
		margin-right: 14px;
		max-width: 184px;
		max-height: 84px;

		&:last-of-type {
			margin-right: 0;
		}

		label {
			padding: 23px 49px;
			font-weight: bold;
			font-size: 28px;
			line-height: 38px;
			text-align: center;
			text-transform: uppercase;
			color: #494949;
			margin-bottom: 0;
			cursor: pointer;
		}

		&.active {
			border: 1.5px solid #0086e6;

			label {
				color: #005592;
			}
		}
	}
}

.btn-preorder-wrapper {
	text-align: center;

	img {
		cursor: pointer;
		max-width: 80%;
		object-fit: contain;
		max-height: 105px;
	}
}

.color {
	display: flex;
	justify-content: center;
	align-items: center;

	.form-check-inline.active {
		label {
			border: 1px solid #000000;
		}
	}
}

label.slt-color {
	border-radius: 50px;
	box-sizing: border-box;
	cursor: pointer;
	margin-bottom: 0;

	span {
		display: block;
		border-radius: 50px;
	}

	&.grey {
		span {
			background-color: #808080;
		}
	}

	&.gold {
		span {
			background-color: #daa520;
		}
	}

	&.silver {
		span {
			background-color: #c0c0c0;
		}
	}

	&.blue {
		span {
			background-color: #0000ff;
		}
	}

	&.pink {
		span {
			background-color: #ffc0cb;
		}
	}

	&.black {
		span {
			background-color: #000000;
		}
	}

	&.white {
		span {
			background-color: #f3f3f3;
			box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.25);
		}
	}

	&.red {
		span {
			background-color: #ff0000;
		}
	}

	&.purple {
		span {
			background-color: #9f64ff;
		}
	}

	&.corange {
		span {
			background-color: #df7537;
		}
	}

	&.deep-blue {
		span {
			background-color: #363945;
		}
	}

	&.light-gold {
		span {
			background-color: #f2f0e9;
		}
	}

	&.space-black {
		span {
			background-color: #454545;
		}
	}

	&.sky-blue {
		span {
			background-color: #dde6eb;
		}
	}

	&.mist-blue {
		span {
			background-color: #9fadc2;
		}
	}

	&.sage {
		span {
			background-color: #b0ba90;
			box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.25);
		}
	}

	&.lavender {
		span {
			background-color: lavender;
		}
	}
}

#desktop {
	nav {
		background-color: #1d1d1d;
		.list-menu {
			display: flex;
			justify-content: center;
			align-items: center;
			.list-item {
				flex: 1;
				text-align: center;
				padding: 9px 0;
				p {
					margin-bottom: 0;
					font-weight: 600;
					font-size: 22px;
					line-height: 116.68%;
					color: #ffffff;
					cursor: pointer;
					&:hover {
						opacity: 0.7;
					}
				}

				&.preorder {
					flex: 2;
					p {
						padding: 10px 0;
						max-width: 343px;
						background: #ff0000;
						border-radius: 5px;
						font-weight: bold;
						font-size: 19px;
						line-height: 116.68%;
					}
				}
			}
		}
	}

	.bang-gia {
		.title {
			padding: 29px 0;
			h4 {
				font-weight: bold;
				font-size: 45px;
				line-height: 116.68%;
				text-align: center;
				color: #000000;
				margin-bottom: 0;
			}
		}
		.slider {
			background: #f7f7f7;
			padding: 46px 28px;

			.prices {
				margin-bottom: 0;

				.item {
					padding: 15px 9px;
					.box {
						background: #ffffff;
						border-radius: 8px;
						padding: 18px 17px;

						&:hover {
							box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
						}

						.btn-scroll-to {
							width: 100%;
							padding: 14px 0;
							margin-top: 11px;
							background: #1d1d1d;
							border-radius: 5px;
							font-weight: bold;
							font-size: 19px;
							line-height: 116.68%;
							color: #ffffff;
							&:hover {
								background: #ffffff;
								border: 1px solid #1d1d1d;
								color: #1d1d1d;
							}
						}
					}
				}

				.slick-prev {
					left: -52px;
				}
				.slick-next {
					right: -52px;
				}

				.slick-prev,
				.slick-next {
					width: 52px;
					height: 52px;
					&:before {
						content: none;
					}
					img {
						width: 100%;
					}
				}

				.slick-dots {
					position: relative;
					bottom: unset;
					margin-top: 30px;
					li {
						margin: 0 24px;
						button {
							width: 22px;
							height: 22px;

							&::before {
								font-size: 22px;
								line-height: normal;
							}
						}
					}
				}
			}
		}
	}

	.cau-hinh {
		.nav-slider {
			margin-top: 45px;
			margin-bottom: 40px;
			.item {
				.box {
					margin: 2px 4px;
					padding: 21px 0;
					background: #ffffff;
					box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					border-radius: 10px;
					max-width: 204px;
					cursor: pointer;
					p {
						font-weight: 600;
						font-size: 19px;
						line-height: 26px;
						text-align: center;
						color: #000000;
						margin-bottom: 0;
					}
				}

				&.slick-current {
					.box {
						background: #0075ff;
						box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
						border-radius: 10px;
						p {
							color: #ffffff;
						}
					}
				}
			}
		}
		.content-slider {
			margin-bottom: 37px;
		}
	}

	.preorder-section {
		margin-bottom: 37px;
		.box {
			background: #f8f8f8;
			border-radius: 10px;
			padding: 47px 49px;

			.product-color {
				display: flex;
				flex-direction: row;
				align-items: center;
				margin-top: 31px;
				margin-bottom: 27px;
				& > p {
					font-size: 20px;
					line-height: 27px;
					text-transform: capitalize;
					color: #0075ff;
					margin-right: 27px;
					margin-bottom: 0;
				}

				.colors {
					display: flex;
					flex-direction: row;
					align-items: center;

					.slt-color {
						margin-right: 13px;
						padding: 3px;
						span {
							width: 26px;
							height: 26px;
						}

						&.selected {
							border: 1px solid #000000;
						}

						&:last-child {
							margin-right: 0;
						}
					}
				}
			}

			.product-picked {
				& > p {
					font-weight: 600;
					font-size: 22px;
					line-height: 30px;
					text-transform: capitalize;
					color: #000000;
					margin-bottom: 19px;
				}

				.picked {
					.picked-box-wrapper {
						display: flex;
						align-items: stretch;
						margin-bottom: 10px;

						.picked-box {
							border: 1px solid #0075ff;
							box-sizing: border-box;
							border-radius: 6px;
							background-color: #fff;
							flex: 1;
							max-width: 137px;
							margin-right: 8px;
							padding: 4px 0;

							&.type {
								display: flex;
								justify-content: center;
								align-items: center;
								font-weight: 600;
								font-size: 13px;
								line-height: 18px;
								text-align: center;
								color: #000000;
							}

							&.option {
								p {
									margin-bottom: 0;
								}
								.capacity {
									font-weight: 600;
									font-size: 13px;
									line-height: 18px;
									text-align: center;
									text-transform: uppercase;
									color: #000000;
								}
								.price {
									font-weight: 600;
									font-size: 13px;
									line-height: 18px;
									text-align: center;
									text-transform: lowercase;
									color: #0075ff;
								}
							}

							&.color {
								display: flex;
								justify-content: center;
								align-items: center;
								font-weight: 600;
								font-size: 13px;
								line-height: 18px;
								text-align: center;
								text-transform: capitalize;
								color: #0075ff;
							}
						}

						.delete {
							flex: 1;
							display: flex;
							align-items: center;
							font-size: 16px;
							line-height: 22px;
							text-align: center;
							text-decoration-line: underline;
							text-transform: capitalize;
							color: #c40000;
							cursor: pointer;
							img {
								margin-right: 7px;
							}
						}
					}
				}
			}

			.product-title {
				font-weight: bold;
				font-size: 42px;
				line-height: 57px;
				color: #000000;
				margin-bottom: 0;
			}

			.product-type {
				display: flex;
				margin-bottom: 18px;

				.type {
					flex: 1;
					max-width: 215px;
					background: #ffffff;
					box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					border-radius: 10px;
					padding: 21px 0;
					cursor: pointer;
					font-weight: 600;
					font-size: 19px;
					line-height: 26px;
					text-align: center;
					color: #000000;
					margin-right: 14px;
					&:last-child {
						margin-right: 0;
					}

					&.selected {
						color: #ffffff;
						background: #0075ff;
						box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					}

					&:hover {
						box-shadow: 0px 0px 15px 10px rgba(0, 0, 0, 0.15);
					}
				}
			}

			.product-options {
				display: flex;
				.option {
					flex: 1;
					cursor: pointer;
					max-width: 215px;
					background: #ffffff;
					box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					border-radius: 10px;
					padding: 17px 0;
					margin-right: 14px;

					.capacity {
						font-size: 20px;
						line-height: 27px;
						text-align: center;
						text-transform: uppercase;
						color: #000000;
						margin-bottom: 0;
					}
					.price {
						font-size: 15px;
						line-height: 20px;
						text-align: center;
						text-decoration-line: line-through;
						text-transform: lowercase;
						color: #a8a8a8;
						margin-bottom: 0;
					}
					.sale {
						font-weight: 600;
						font-size: 20px;
						line-height: 27px;
						text-align: center;
						text-transform: lowercase;
						color: #0075ff;
						margin-bottom: 0;
					}

					&:last-child {
						margin-right: 0;
					}

					&.selected {
						border: 1px solid #0075ff;
						box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					}

					&:hover {
						box-shadow: 0px 0px 15px 10px rgba(0, 0, 0, 0.15);
					}
				}
			}
			.khuyen-mai {
				h4 {
					font-weight: 600;
					font-size: 22px;
					line-height: 30px;
					text-transform: capitalize;
					color: #c40000;
				}
				ol {
					padding-left: 1rem;
					li {
						&::marker {
							font-size: 22px;
						}
						p {
							font-size: 22px;
							line-height: 240.68%;
							color: #2e2e2e;
							margin-bottom: 0;
							span {
								font-weight: 600;
								font-size: 22px;
								line-height: 240.68%;
								color: #c40000;
							}
						}

						.form-check {
							label {
								font-size: 20px;
								line-height: 186.68%;
								color: #2e2e2e;
								margin-bottom: 0;
								cursor: pointer;
								span {
									font-weight: 600;
									font-size: 20px;
									line-height: 186.68%;
									color: #c40000;
								}
							}

							.checkmark {
								border: 1px solid #000;
								width: 20px;
								height: 20px;
								border-radius: 50px;
								position: absolute;
								left: -5px;
								top: 10px;
							}

							input:checked ~ .checkmark {
								background: #c40000;
								&::after {
									content: "";
									position: absolute;
									width: 19px;
									height: 19px;
									border-radius: 50%;
									border: solid 3px white;
									top: 50%;
									left: 50%;
									transform: translate(-50%, -50%) rotate(45deg);
								}
							}
						}
					}
				}
			}

			.btn-wrapper {
				margin-top: 55px;
				text-align: center;
				.btn {
					background: #c40000;
					box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					border-radius: 10px;
					width: 100%;
					max-width: 500px;
					padding: 18px 0;
					font-weight: bold;
					font-size: 22px;
					line-height: 30px;
					text-align: center;
					text-transform: capitalize;
					color: #ffffff;

					&:hover {
						opacity: 0.7;
					}
				}
			}
		}
		&.iphone {
			.product-title {
				margin-bottom: 19px;
			}
			.khuyen-mai {
				margin-bottom: 55px;
				h4 {
					margin-bottom: 11px;
				}
			}
		}
		&.ipad {
			.product-title {
				margin-bottom: 168px;
			}
		}
	}

	.gifts {
		background-color: #252525;
		padding-top: 41px;
		padding-bottom: 14px;

		.title {
			margin-bottom: 48px;
			h4 {
				font-weight: 600;
				font-size: 38px;
				line-height: 73px;
				text-align: center;
				letter-spacing: 0.01em;
				text-transform: capitalize;
				color: #ffffff;
				margin-bottom: 0;
			}

			h3 {
				font-weight: bold;
				font-size: 64px;
				line-height: 73px;
				text-align: center;
				letter-spacing: 0.01em;
				text-transform: capitalize;
				color: #ffffff;
				margin-bottom: 0;
			}
		}

		.list-items {
			display: flex;
			justify-content: center;
			flex-direction: row;
			flex-wrap: wrap;
			.list-item {
				flex: 0 0 20%;
				padding: 0 9px;
				.box {
					background: #ffffff;
					border-radius: 8px;
					padding: 8px 12px;
					margin-bottom: 18px;

					& > img {
						margin-bottom: 8px;
					}

					.capacity {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;

						span {
							&:not(.number) {
								width: 30px;
								height: 30px;
								cursor: pointer;
								img {
									width: 100%;
								}
								&:hover {
									opacity: 0.7;
								}
							}
							&.number {
								font-weight: 600;
								font-size: 32px;
								line-height: 116.68%;
								color: #434343;
								margin-left: 15px;
								margin-right: 15px;
							}
						}
					}
				}
				.color {
					justify-content: flex-start;
					padding-bottom: 37px;
					& > span {
						font-weight: bold;
						font-size: 20px;
						line-height: 27px;
						text-transform: capitalize;
						color: #ffffff;
						margin-right: 21px;
					}

					.selector-color {
						display: flex;
						justify-content: center;
						align-items: center;
						label {
							margin-right: 6px;
							padding: 2px;
							span {
								width: 26px;
								height: 26px;
								border-radius: 50%;
							}
							&.selected {
								border: 1px solid #fff;
							}

							&:last-child {
								margin-right: 0;
							}
						}
					}
				}
			}
		}
	}

	.preorder-form {
		.box {
			background: #efefef;
			border-radius: 10px;
			margin: 33px auto;
			padding: 40px 63px;

			.list {
				& > p {
					font-weight: bold;
					font-size: 25px;
					line-height: 34px;
					text-transform: capitalize;
					color: #c30000;
				}
				.list-product {
					.product {
						margin-bottom: 10px;
						.info-product {
							.type {
								font-weight: bold;
								font-size: 15px;
								line-height: 20px;
								text-transform: capitalize;
								color: #000000;
								margin-bottom: 0;
							}
							.option,
							.quantity {
								font-weight: 600;
								font-size: 15px;
								line-height: 20px;
								text-transform: capitalize;
								color: #000000;
								margin-bottom: 0;
								span {
									font-weight: normal;
									font-size: 15px;
									line-height: 20px;
									text-transform: capitalize;
									color: #000000;
								}
							}
						}
					}
				}
			}

			.form {
				p {
					font-weight: bold;
					font-size: 25px;
					line-height: 34px;
					color: #303030;
					margin-bottom: 0;
				}

				input {
					background: #ffffff;
					border-radius: 10px;
					font-size: 18px;
					line-height: 25px;
					color: #808080;
					margin-bottom: 17px;
				}

				span.note {
					font-size: 11px;
					line-height: 13px;
					color: #ff2727;
				}

				.btn {
					margin-top: 6px;
					background: #c40000;
					border-radius: 12px;
					font-weight: bold;
					font-size: 25px;
					line-height: 34px;
					text-transform: uppercase;
					color: #ffffff;
					width: 100%;
					padding: 12px 0;
					max-height: 59px;
				}
			}
		}
	}
}

#mobile {
	nav {
		background-color: #1d1d1d;
		.list-menu {
			display: flex;
			justify-content: center;
			align-items: center;
			.list-item {
				flex: 1;
				text-align: center;
				padding: 9px 0;

				&.preorder {
					flex: 1;
					p {
						margin-bottom: 0;
						color: #ffffff;
						cursor: pointer;
						padding: 9px 0;
						background: #ff0000;
						border-radius: 3px;
						font-weight: bold;
						font-size: 16px;
						line-height: 116.68%;
					}
				}
			}
		}
	}

	.bang-gia {
		.title {
			padding: 23px 0;
			h4 {
				font-weight: bold;
				font-size: 24px;
				line-height: 116.68%;
				text-align: center;
				color: #000000;
				margin-bottom: 0;
			}
		}
		.slider {
			background: #f7f7f7;
			padding: 23px 0 25px;

			.prices {
				margin-bottom: 0;

				.item {
					padding: 15px 9px;
					.box {
						background: #ffffff;
						border-radius: 8px;
						padding: 11px;

						&:hover {
							box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
						}

						.btn-scroll-to {
							width: 100%;
							padding: 10px 0;
							margin-top: 11px;
							background: #1d1d1d;
							border-radius: 5px;
							font-weight: bold;
							font-size: 11px;
							line-height: 116.68%;
							color: #ffffff;
							&:hover {
								background: #ffffff;
								border: 1px solid #1d1d1d;
								color: #1d1d1d;
							}
						}
					}
				}
			}
		}
	}

	.cau-hinh {
		.nav-slider {
			margin-top: 35px;
			margin-bottom: 35px;
			.item {
				.box {
					margin: 2px 4px;
					padding: 21px 0;
					background: #ffffff;
					box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					border-radius: 10px;
					max-width: 204px;
					cursor: pointer;
					p {
						font-weight: 600;
						font-size: 19px;
						line-height: 26px;
						text-align: center;
						color: #000000;
						margin-bottom: 0;
					}
				}

				&.slick-current {
					.box {
						background: #0075ff;
						box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
						border-radius: 10px;
						p {
							color: #ffffff;
						}
					}
				}
			}
			.slick-list {
				padding: 0 !important;
				.slick-track {
					width: 100% !important;
					transform: unset !important;
					display: flex;
					flex-wrap: wrap;

					.item {
						flex: 0 0 33.33%;
						margin-bottom: 10px;
						padding: 0 5px;
						.box {
							padding: 13px 0;
							p {
								font-size: 12px;
								line-height: 16px;
							}
						}
					}
				}
			}
		}
		.content-slider {
			margin-bottom: 32px;
		}
	}

	.preorder-section {
		margin-bottom: 32px;
		.box {
			background: #f8f8f8;
			border-radius: 10px;
			padding: 36px 5px 39px;

			.product-img {
				margin-bottom: 10px;
				.img {
					text-align: center;
				}
				.img-fluid {
					max-width: 300px;
				}
			}

			.product-color {
				display: flex;
				flex-direction: row;
				align-items: center;
				margin-bottom: 27px;
				& > p {
					font-size: 14px;
					line-height: 1;
					text-transform: capitalize;
					color: #0075ff;
					margin-right: 27px;
					margin-bottom: 0;
				}

				.colors {
					display: flex;
					flex-direction: row;
					align-items: center;

					.slt-color {
						margin-right: 13px;
						padding: 3px;
						span {
							width: 18px;
							height: 18px;
						}

						&.selected {
							border: 1px solid #000000;
						}

						&:last-child {
							margin-right: 0;
						}
					}
				}
			}

			.product-picked {
				& > p {
					font-weight: 600;
					font-size: 18px;
					line-height: 25px;
					text-transform: capitalize;
					color: #000000;
					margin-bottom: 18px;
				}

				.picked {
					.picked-box-wrapper {
						display: flex;
						align-items: stretch;
						margin-bottom: 10px;

						.picked-box {
							border: 1px solid #0075ff;
							box-sizing: border-box;
							border-radius: 6px;
							background-color: #fff;
							flex: 1;
							max-width: 137px;
							margin-right: 8px;
							padding: 4px 0;

							&.type {
								display: flex;
								justify-content: center;
								align-items: center;
								font-weight: 600;
								font-size: 10px;
								line-height: 14px;
								text-align: center;
								color: #000000;
							}

							&.option {
								p {
									margin-bottom: 0;
								}
								.capacity {
									font-weight: 600;
									font-size: 10px;
									line-height: 14px;
									text-align: center;
									text-transform: uppercase;
									color: #000000;
								}
								.price {
									font-weight: 600;
									font-size: 10px;
									line-height: 14px;
									text-align: center;
									text-transform: lowercase;
									color: #0075ff;
								}
							}

							&.color {
								display: flex;
								justify-content: center;
								align-items: center;
								font-weight: 600;
								font-size: 10px;
								line-height: 14px;
								text-align: center;
								text-transform: capitalize;
								color: #0075ff;
							}
						}

						.delete {
							flex: 1;
							display: flex;
							align-items: center;
							font-size: 10px;
							line-height: 14px;
							text-align: center;
							text-decoration-line: underline;
							text-transform: capitalize;
							color: #c40000;
							cursor: pointer;
							img {
								margin-right: 7px;
							}
						}
					}
				}
			}

			.product-title {
				font-weight: bold;
				font-size: 30px;
				line-height: 41px;
				color: #000000;
				margin-bottom: 0;
				text-align: center;
			}

			.product-type {
				display: flex;
				flex-wrap: wrap;
				margin-bottom: 18px;

				.type {
					flex: 0 0 calc(50% - 12px);
					background: #ffffff;
					box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					border-radius: 10px;
					padding: 14px 0;
					cursor: pointer;
					font-weight: 600;
					font-size: 16px;
					line-height: 22px;
					text-align: center;
					color: #000000;
					margin-left: 6px;
					margin-right: 6px;
					margin-bottom: 10px;

					&.selected {
						color: #ffffff;
						background: #0075ff;
						box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					}

					&:hover {
						box-shadow: 0px 0px 15px 5px rgba(0, 0, 0, 0.15);
					}
				}
			}

			.product-options {
				display: flex;
				flex-wrap: wrap;
				.option {
					flex: 0 0 calc(50% - 12px);
					cursor: pointer;
					background: #ffffff;
					box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					border-radius: 10px;
					padding: 4px 0;
					margin-left: 6px;
					margin-right: 6px;
					margin-bottom: 10px;

					.capacity {
						font-size: 17px;
						line-height: 23px;
						text-align: center;
						text-transform: uppercase;
						color: #000000;
						margin-bottom: 0;
					}
					.price {
						font-size: 14px;
						line-height: 19px;
						text-align: center;
						text-decoration-line: line-through;
						text-transform: lowercase;
						color: #a8a8a8;
						margin-bottom: 0;
					}
					.sale {
						font-weight: 600;
						font-size: 18px;
						line-height: 25px;
						text-align: center;
						text-transform: lowercase;
						color: #0075ff;
						margin-bottom: 0;
					}

					&.selected {
						border: 1px solid #0075ff;
						box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					}

					&:hover {
						box-shadow: 0px 0px 15px 5px rgba(0, 0, 0, 0.15);
					}
				}
			}

			.khuyen-mai {
				h4 {
					font-weight: 600;
					font-size: 18px;
					line-height: 25px;
					text-transform: capitalize;
					color: #c40000;
				}
				ol {
					padding-left: 1rem;
					li {
						&::marker {
							font-size: 16px;
						}
						p {
							font-size: 16px;
							line-height: 150.68%;
							color: #2e2e2e;
							margin-bottom: 0;
							span {
								font-weight: 600;
								font-size: 16px;
								line-height: 150.68%;
								color: #c40000;
							}
						}

						.form-check {
							label {
								font-size: 16px;
								line-height: 186.68%;
								color: #2e2e2e;
								margin-bottom: 0;
								cursor: pointer;
								span {
									font-weight: 600;
									font-size: 16px;
									line-height: 186.68%;
									color: #c40000;
								}
							}

							.checkmark {
								border: 1px solid #000;
								width: 16px;
								height: 16px;
								border-radius: 50px;
								position: absolute;
								left: -5px;
								top: 7px;
							}

							input:checked ~ .checkmark {
								background: #c40000;
								&::after {
									content: "";
									position: absolute;
									width: 15px;
									height: 15px;
									border-radius: 50%;
									border: solid 3px white;
									top: 50%;
									left: 50%;
									transform: translate(-50%, -50%) rotate(45deg);
								}
							}
						}
					}
				}
			}

			.btn-wrapper {
				margin-top: 32px;
				text-align: center;
				.btn {
					background: #c40000;
					box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
					border-radius: 10px;
					width: 100%;
					max-width: 500px;
					padding: 11px 0;
					font-weight: bold;
					font-size: 19px;
					line-height: 26px;
					text-align: center;
					text-transform: capitalize;
					color: #ffffff;

					&:hover {
						opacity: 0.7;
					}
				}
			}
		}
	}

	.gifts {
		background-color: #252525;
		padding-top: 31px;
		padding-left: 0;
		padding-right: 0;

		.title {
			margin-bottom: 48px;
			h4 {
				font-weight: 600;
				font-size: 25px;
				line-height: 34px;
				text-align: center;
				letter-spacing: 0.01em;
				text-transform: capitalize;
				color: #ffffff;
				margin-bottom: 0;
			}

			h3 {
				font-weight: bold;
				font-size: 36px;
				line-height: 45px;
				text-align: center;
				letter-spacing: 0.01em;
				text-transform: capitalize;
				color: #ffffff;
				margin-bottom: 0;
			}
		}

		.list-items {
			.list-item {
				padding: 0 6px;
				.box {
					background: #ffffff;
					border-radius: 8px;
					padding: 8px 12px;

					& > img {
						margin-bottom: 8px;
					}

					.capacity {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;

						span {
							&:not(.number) {
								width: 20px;
								height: 20px;
								cursor: pointer;
								img {
									width: 100%;
								}
								&:hover {
									opacity: 0.7;
								}
							}
							&.number {
								font-weight: 600;
								font-size: 15px;
								line-height: 116.68%;
								color: #434343;
								margin-left: 14px;
								margin-right: 14px;
							}
						}
					}
				}
				.color {
					justify-content: flex-start;
					padding-top: 18px;
					padding-bottom: 25px;
					& > span {
						font-weight: bold;
						font-size: 9px;
						text-transform: capitalize;
						color: #ffffff;
						margin-right: 11px;
					}

					.selector-color {
						display: flex;
						justify-content: center;
						align-items: center;
						label {
							margin-right: 6px;
							padding: 2px;
							span {
								width: 16px;
								height: 16px;
								border-radius: 50%;
							}
							&.selected {
								border: 1px solid #fff;
							}

							&:last-child {
								margin-right: 0;
							}
						}
					}
				}
			}
		}
	}

	.preorder-form {
		.box {
			background: #efefef;
			border-radius: 10px;
			margin: 18px auto;
			padding: 33px 16px 35px;

			.list {
				& > p {
					font-weight: bold;
					font-size: 25px;
					line-height: 34px;
					text-transform: capitalize;
					color: #c30000;
				}
				.list-product {
					.product {
						margin-bottom: 10px;
						.img-product {
							padding-right: 0;
						}
						.info-product {
							.type {
								font-weight: bold;
								font-size: 15px;
								line-height: 20px;
								text-transform: capitalize;
								color: #000000;
								margin-bottom: 0;
							}
							.option,
							.quantity {
								font-weight: 600;
								font-size: 15px;
								line-height: 20px;
								text-transform: capitalize;
								color: #000000;
								margin-bottom: 0;
								span {
									font-weight: normal;
									font-size: 15px;
									line-height: 20px;
									text-transform: capitalize;
									color: #000000;
								}
							}
						}
					}
				}
			}

			.form {
				p {
					font-weight: bold;
					font-size: 18px;
					line-height: 25px;
					color: #303030;
					margin-bottom: 0;
				}

				input {
					background: #ffffff;
					border-radius: 10px;
					font-size: 12px;
					line-height: 16px;
					color: #808080;
					margin-bottom: 15px;
				}

				span.note {
					font-size: 8px;
					line-height: 9px;
					color: #ff2727;
				}

				.btn {
					margin-top: 6px;
					background: #c40000;
					border-radius: 12px;
					font-weight: bold;
					font-size: 15px;
					line-height: 20px;
					text-transform: uppercase;
					color: #ffffff;
					width: 100%;
					padding: 10px 0;
					max-height: 59px;
				}
			}
		}
	}
}

@media screen and (min-width: 768px) {
	.container,
	.container-lg,
	.container-md,
	.container-sm,
	.container-xl {
		max-width: 1138px;
	}
}

@media screen and (min-width: 992px) {
	.container,
	.container-lg,
	.container-md,
	.container-sm,
	.container-xl {
		max-width: 1362px;
	}
}

@media screen and (min-width: 1200px) {
	.container,
	.container-lg,
	.container-md,
	.container-sm,
	.container-xl {
		max-width: 1570px;
	}
}

@media screen and (max-width: 1199px) {
	#desktop .gifts .list-items .list-item {
		flex: 0 0 33%;
	}
}

@media screen and (max-width: 767px) {
}

@media screen and (max-width: 320px) {
}

@media screen and (max-width: 375px) {
}

// @import "modal.scss";
@import "phone-btn.scss";
// @import "animation.scss";
