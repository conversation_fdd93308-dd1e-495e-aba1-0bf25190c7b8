@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap");
@import "phone-btn.scss";
body {
font-family: "Open Sans", sans-serif;
}
.img-fluid {
width: 100%;
}
.banner {
cursor: pointer;
}
input[type="radio"] {
display: none;
}
.ip_type {
text-align: center;
}
.ip_type .form-check-inline {
box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
border-radius: 18px;
}
.ip_type .form-check-inline label {
    padding: 19px 21px;
    font-weight: 600;
    font-size: 22px;
    line-height: 30px;
    text-align: center;
    text-transform: capitalize;
    color: #000;
    cursor: pointer;
    margin-bottom: 0;
}
.ip_type .form-check-inline.active {
    background: #005592;
}
.ip_type .form-check-inline.active label {
    color: #fff;
}
.ip_capacity {
    text-align: center;
}
.ip_capacity .form-check-inline {
    background: #fff;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 18px;
    margin-right: 14px;
    max-width: 184px;
    max-height: 84px;
}
.ip_capacity .form-check-inline:last-of-type {
    margin-right: 0;
}
.ip_capacity .form-check-inline label {
    padding: 23px 49px;
    font-weight: bold;
    font-size: 28px;
    line-height: 38px;
    text-align: center;
    text-transform: uppercase;
    color: #494949;
    margin-bottom: 0;
    cursor: pointer;
}
.ip_capacity .form-check-inline.active {
    border: 1.5px solid #0086e6;
}
.ip_capacity .form-check-inline.active label {
    color: #005592;
}
.btn-preorder-wrapper {
    text-align: center;
}
.btn-preorder-wrapper img {
    cursor: pointer;
    max-width: 80%;
    object-fit: contain;
    max-height: 105px;
}
.color {
    display: flex;
    justify-content: center;
    align-items: center;
}
.color .form-check-inline.active label {
    border: 1px solid #000;
}
label.slt-color {
    border-radius: 50px;
    box-sizing: border-box;
    cursor: pointer;
    margin-bottom: 0;
}
label.slt-color span {
    display: block;
    border-radius: 50px;
}
label.slt-color.grey span {
    background-color: #808080;
}
label.slt-color.gold span {
    background-color: #daa520;
}
label.slt-color.silver span {
    background-color: #c0c0c0;
}
label.slt-color.blue span {
    background-color: #00f;
}
label.slt-color.pink span {
    background-color: #ffc0cb;
}
label.slt-color.black span {
    background-color: #000;
}
label.slt-color.white span {
    background-color: #f3f3f3;
    box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.25);
}
label.slt-color.red span {
    background-color: #f00;
}
label.slt-color.purple span {
    background-color: #9f64ff;
}
label.slt-color.corange span {
    background-color: #df7537;
}
label.slt-color.deep-blue span {
    background-color: #363945;
    box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.25);
}
label.slt-color.light-gold span {
    background-color: #f2f0e9;
    box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.25);
}
label.slt-color.space-black span {
    background-color: #454545;
}
label.slt-color.sky-blue span {
    background-color: #dde6eb;
    box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.25);
}
label.slt-color.mist-blue span {
    background-color: #9fadc2;
    box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.25);
}
label.slt-color.sage span {
    background-color: #b0ba90;
}
label.slt-color.lavender span {
    background-color: lavender;
    box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.25);
}
#desktop nav {
    background-color: #1d1d1d;
}
#desktop nav .list-menu {
    display: flex;
    justify-content: center;
    align-items: center;
}
#desktop nav .list-menu .list-item {
    flex: 1;
    text-align: center;
    padding: 9px 0;
}
#desktop nav .list-menu .list-item p {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 22px;
    line-height: 116.68%;
    color: #fff;
    cursor: pointer;
}
#desktop nav .list-menu .list-item p:hover {
    opacity: 0.7;
}
#desktop nav .list-menu .list-item.preorder {
    flex: 2;
}
#desktop nav .list-menu .list-item.preorder p {
    padding: 10px 0;
    max-width: 343px;
    background: #f00;
    border-radius: 5px;
    font-weight: bold;
    font-size: 19px;
    line-height: 116.68%;
}
#desktop .bang-gia .title {
    padding: 29px 0;
}
#desktop .bang-gia .title h4 {
    font-weight: bold;
    font-size: 45px;
    line-height: 116.68%;
    text-align: center;
    color: #000;
    margin-bottom: 0;
}
#desktop .bang-gia .slider {
    background: #f7f7f7;
    padding: 46px 28px;
}
#desktop .bang-gia .slider .prices {
    margin-bottom: 0;
}
#desktop .bang-gia .slider .prices .item {
    padding: 15px 9px;
}
#desktop .bang-gia .slider .prices .item .box {
    background: #fff;
    border-radius: 8px;
    padding: 18px 17px;
}
#desktop .bang-gia .slider .prices .item .box:hover {
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
}
#desktop .bang-gia .slider .prices .item .box .btn-scroll-to {
    width: 100%;
    padding: 14px 0;
    margin-top: 11px;
    background: #1d1d1d;
    border-radius: 5px;
    font-weight: bold;
    font-size: 19px;
    line-height: 116.68%;
    color: #fff;
}
#desktop .bang-gia .slider .prices .item .box .btn-scroll-to:hover {
    background: #fff;
    border: 1px solid #1d1d1d;
    color: #1d1d1d;
}
#desktop .bang-gia .slider .prices .slick-prev {
    left: -52px;
}
#desktop .bang-gia .slider .prices .slick-next {
    right: -52px;
}
#desktop .bang-gia .slider .prices .slick-prev, #desktop .bang-gia .slider .prices .slick-next {
    width: 52px;
    height: 52px;
}
#desktop .bang-gia .slider .prices .slick-prev:before, #desktop .bang-gia .slider .prices .slick-next:before {
    content: none;
}
#desktop .bang-gia .slider .prices .slick-prev img, #desktop .bang-gia .slider .prices .slick-next img {
    width: 100%;
}
#desktop .bang-gia .slider .prices .slick-dots {
    position: relative;
    bottom: unset;
    margin-top: 30px;
}
#desktop .bang-gia .slider .prices .slick-dots li {
    margin: 0 24px;
}
#desktop .bang-gia .slider .prices .slick-dots li button {
    width: 22px;
    height: 22px;
}
#desktop .bang-gia .slider .prices .slick-dots li button::before {
    font-size: 22px;
    line-height: normal;
}
#desktop .cau-hinh .nav-slider {
    margin-top: 45px;
    margin-bottom: 40px;
}
#desktop .cau-hinh .nav-slider .item .box {
    margin: 2px 4px;
    padding: 21px 0;
    background: #fff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    max-width: 204px;
    cursor: pointer;
}
#desktop .cau-hinh .nav-slider .item .box p {
    font-weight: 600;
    font-size: 19px;
    line-height: 26px;
    text-align: center;
    color: #000;
    margin-bottom: 0;
}
#desktop .cau-hinh .nav-slider .item.slick-current .box {
    background: #0075ff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}
#desktop .cau-hinh .nav-slider .item.slick-current .box p {
    color: #fff;
}
#desktop .cau-hinh .content-slider {
    margin-bottom: 37px;
}
#desktop .preorder-section {
    margin-bottom: 37px;
}
#desktop .preorder-section .box {
    background: #f8f8f8;
    border-radius: 10px;
    padding: 47px 49px;
}
#desktop .preorder-section .box .product-color {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 31px;
    margin-bottom: 27px;
}
#desktop .preorder-section .box .product-color > p {
    font-size: 20px;
    line-height: 27px;
    text-transform: capitalize;
    color: #0075ff;
    margin-right: 27px;
    margin-bottom: 0;
}
#desktop .preorder-section .box .product-color .colors {
    display: flex;
    flex-direction: row;
    align-items: center;
}
#desktop .preorder-section .box .product-color .colors .slt-color {
    margin-right: 13px;
    padding: 3px;
}
#desktop .preorder-section .box .product-color .colors .slt-color span {
    width: 26px;
    height: 26px;
}
#desktop .preorder-section .box .product-color .colors .slt-color.selected {
    border: 1px solid #000;
}
#desktop .preorder-section .box .product-color .colors .slt-color:last-child {
    margin-right: 0;
}
#desktop .preorder-section .box .product-picked > p {
    font-weight: 600;
    font-size: 22px;
    line-height: 30px;
    text-transform: capitalize;
    color: #000;
    margin-bottom: 19px;
}
#desktop .preorder-section .box .product-picked .picked .picked-box-wrapper {
    display: flex;
    align-items: stretch;
    margin-bottom: 10px;
}
#desktop .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box {
    border: 1px solid #0075ff;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: #fff;
    flex: 1;
    max-width: 137px;
    margin-right: 8px;
    padding: 4px 0;
}
#desktop .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box.type {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    font-size: 13px;
    line-height: 18px;
    text-align: center;
    color: #000;
}
#desktop .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box.option p {
    margin-bottom: 0;
}
#desktop .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box.option .capacity {
    font-weight: 600;
    font-size: 13px;
    line-height: 18px;
    text-align: center;
    text-transform: uppercase;
    color: #000;
}
#desktop .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box.option .price {
    font-weight: 600;
    font-size: 13px;
    line-height: 18px;
    text-align: center;
    text-transform: lowercase;
    color: #0075ff;
}
#desktop .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box.color {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    font-size: 13px;
    line-height: 18px;
    text-align: center;
    text-transform: capitalize;
    color: #0075ff;
}
#desktop .preorder-section .box .product-picked .picked .picked-box-wrapper .delete {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    text-decoration-line: underline;
    text-transform: capitalize;
    color: #c40000;
    cursor: pointer;
}
#desktop .preorder-section .box .product-picked .picked .picked-box-wrapper .delete img {
    margin-right: 7px;
}
#desktop .preorder-section .box .product-title {
    font-weight: bold;
    font-size: 42px;
    line-height: 57px;
    color: #000;
    margin-bottom: 0;
}
#desktop .preorder-section .box .product-type {
    display: flex;
    margin-bottom: 18px;
}
#desktop .preorder-section .box .product-type .type {
    flex: 1;
    max-width: 215px;
    background: #fff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 21px 0;
    cursor: pointer;
    font-weight: 600;
    font-size: 19px;
    line-height: 26px;
    text-align: center;
    color: #000;
    margin-right: 14px;
}
#desktop .preorder-section .box .product-type .type:last-child {
    margin-right: 0;
}
#desktop .preorder-section .box .product-type .type.selected {
    color: #fff;
    background: #0075ff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
}
#desktop .preorder-section .box .product-type .type:hover {
    box-shadow: 0px 0px 15px 10px rgba(0, 0, 0, 0.15);
}
#desktop .preorder-section .box .product-options {
    display: flex;
}
#desktop .preorder-section .box .product-options .option {
    flex: 1;
    cursor: pointer;
    max-width: 215px;
    background: #fff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 17px 0;
    margin-right: 14px;
}
#desktop .preorder-section .box .product-options .option .capacity {
    font-size: 20px;
    line-height: 27px;
    text-align: center;
    text-transform: uppercase;
    color: #000;
    margin-bottom: 0;
}
#desktop .preorder-section .box .product-options .option .price {
    font-size: 15px;
    line-height: 20px;
    text-align: center;
    text-decoration-line: line-through;
    text-transform: lowercase;
    color: #a8a8a8;
    margin-bottom: 0;
}
#desktop .preorder-section .box .product-options .option .sale {
    font-weight: 600;
    font-size: 20px;
    line-height: 27px;
    text-align: center;
    text-transform: lowercase;
    color: #0075ff;
    margin-bottom: 0;
}
#desktop .preorder-section .box .product-options .option:last-child {
    margin-right: 0;
}
#desktop .preorder-section .box .product-options .option.selected {
    border: 1px solid #0075ff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
}
#desktop .preorder-section .box .product-options .option:hover {
    box-shadow: 0px 0px 15px 10px rgba(0, 0, 0, 0.15);
}
#desktop .preorder-section .box .khuyen-mai h4 {
    font-weight: 600;
    font-size: 22px;
    line-height: 30px;
    text-transform: capitalize;
    color: #c40000;
}
#desktop .preorder-section .box .khuyen-mai ol {
    padding-left: 1rem;
}
#desktop .preorder-section .box .khuyen-mai ol li::marker {
    font-size: 22px;
}
#desktop .preorder-section .box .khuyen-mai ol li p {
    font-size: 22px;
    line-height: 240.68%;
    color: #2e2e2e;
    margin-bottom: 0;
}
#desktop .preorder-section .box .khuyen-mai ol li p span {
    font-weight: 600;
    font-size: 22px;
    line-height: 240.68%;
    color: #c40000;
}
#desktop .preorder-section .box .khuyen-mai ol li .form-check label {
    font-size: 20px;
    line-height: 186.68%;
    color: #2e2e2e;
    margin-bottom: 0;
    cursor: pointer;
}
#desktop .preorder-section .box .khuyen-mai ol li .form-check label span {
    font-weight: 600;
    font-size: 20px;
    line-height: 186.68%;
    color: #c40000;
}
#desktop .preorder-section .box .khuyen-mai ol li .form-check .checkmark {
    border: 1px solid #000;
    width: 20px;
    height: 20px;
    border-radius: 50px;
    position: absolute;
    left: -5px;
    top: 10px;
}
#desktop .preorder-section .box .khuyen-mai ol li .form-check input:checked ~ .checkmark {
    background: #c40000;
}
#desktop .preorder-section .box .khuyen-mai ol li .form-check input:checked ~ .checkmark::after {
    content: "";
    position: absolute;
    width: 19px;
    height: 19px;
    border-radius: 50%;
    border: solid 3px white;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
}
#desktop .preorder-section .box .btn-wrapper {
    margin-top: 55px;
    text-align: center;
}
#desktop .preorder-section .box .btn-wrapper .btn {
    background: #c40000;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    width: 100%;
    max-width: 500px;
    padding: 18px 0;
    font-weight: bold;
    font-size: 22px;
    line-height: 30px;
    text-align: center;
    text-transform: capitalize;
    color: #fff;
}
#desktop .preorder-section .box .btn-wrapper .btn:hover {
    opacity: 0.7;
}
#desktop .preorder-section.iphone .product-title {
    margin-bottom: 19px;
}
#desktop .preorder-section.iphone .khuyen-mai {
    margin-bottom: 55px;
}
#desktop .preorder-section.iphone .khuyen-mai h4 {
    margin-bottom: 11px;
}
#desktop .preorder-section.ipad .product-title {
    margin-bottom: 168px;
}
#desktop .gifts {
    background-color: #252525;
    padding-top: 41px;
    padding-bottom: 14px;
}
#desktop .gifts .title {
    margin-bottom: 48px;
}
#desktop .gifts .title h4 {
    font-weight: 600;
    font-size: 38px;
    line-height: 73px;
    text-align: center;
    letter-spacing: 0.01em;
    text-transform: capitalize;
    color: #fff;
    margin-bottom: 0;
}
#desktop .gifts .title h3 {
    font-weight: bold;
    font-size: 64px;
    line-height: 73px;
    text-align: center;
    letter-spacing: 0.01em;
    text-transform: capitalize;
    color: #fff;
    margin-bottom: 0;
}
#desktop .gifts .list-items {
    display: flex;
    justify-content: center;
    flex-direction: row;
    flex-wrap: wrap;
}
#desktop .gifts .list-items .list-item {
    flex: 0 0 20%;
    padding: 0 9px;
}
#desktop .gifts .list-items .list-item .box {
    background: #fff;
    border-radius: 8px;
    padding: 8px 12px;
    margin-bottom: 18px;
}
#desktop .gifts .list-items .list-item .box > img {
    margin-bottom: 8px;
}
#desktop .gifts .list-items .list-item .box .capacity {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}
#desktop .gifts .list-items .list-item .box .capacity span:not(.number) {
    width: 30px;
    height: 30px;
    cursor: pointer;
}
#desktop .gifts .list-items .list-item .box .capacity span:not(.number) img {
    width: 100%;
}
#desktop .gifts .list-items .list-item .box .capacity span:not(.number):hover {
    opacity: 0.7;
}
#desktop .gifts .list-items .list-item .box .capacity span.number {
    font-weight: 600;
    font-size: 32px;
    line-height: 116.68%;
    color: #434343;
    margin-left: 15px;
    margin-right: 15px;
}
#desktop .gifts .list-items .list-item .color {
    justify-content: flex-start;
    padding-bottom: 37px;
}
#desktop .gifts .list-items .list-item .color > span {
    font-weight: bold;
    font-size: 20px;
    line-height: 27px;
    text-transform: capitalize;
    color: #fff;
    margin-right: 21px;
}
#desktop .gifts .list-items .list-item .color .selector-color {
    display: flex;
    justify-content: center;
    align-items: center;
}
#desktop .gifts .list-items .list-item .color .selector-color label {
    margin-right: 6px;
    padding: 2px;
}
#desktop .gifts .list-items .list-item .color .selector-color label span {
    width: 26px;
    height: 26px;
    border-radius: 50%;
}
#desktop .gifts .list-items .list-item .color .selector-color label.selected {
    border: 1px solid #fff;
}
#desktop .gifts .list-items .list-item .color .selector-color label:last-child {
    margin-right: 0;
}
#desktop .preorder-form .box {
    background: #efefef;
    border-radius: 10px;
    margin: 33px auto;
    padding: 40px 63px;
}
#desktop .preorder-form .box .list > p {
    font-weight: bold;
    font-size: 25px;
    line-height: 34px;
    text-transform: capitalize;
    color: #c30000;
}
#desktop .preorder-form .box .list .list-product .product {
    margin-bottom: 10px;
}
#desktop .preorder-form .box .list .list-product .product .info-product .type {
    font-weight: bold;
    font-size: 15px;
    line-height: 20px;
    text-transform: capitalize;
    color: #000;
    margin-bottom: 0;
}
#desktop .preorder-form .box .list .list-product .product .info-product .option, #desktop .preorder-form .box .list .list-product .product .info-product .quantity {
    font-weight: 600;
    font-size: 15px;
    line-height: 20px;
    text-transform: capitalize;
    color: #000;
    margin-bottom: 0;
}
#desktop .preorder-form .box .list .list-product .product .info-product .option span, #desktop .preorder-form .box .list .list-product .product .info-product .quantity span {
    font-weight: normal;
    font-size: 15px;
    line-height: 20px;
    text-transform: capitalize;
    color: #000;
}
#desktop .preorder-form .box .form p {
    font-weight: bold;
    font-size: 25px;
    line-height: 34px;
    color: #303030;
    margin-bottom: 0;
}
#desktop .preorder-form .box .form input {
    background: #fff;
    border-radius: 10px;
    font-size: 18px;
    line-height: 25px;
    color: #808080;
    margin-bottom: 17px;
}
#desktop .preorder-form .box .form span.note {
    font-size: 11px;
    line-height: 13px;
    color: #ff2727;
}
#desktop .preorder-form .box .form .btn {
    margin-top: 6px;
    background: #c40000;
    border-radius: 12px;
    font-weight: bold;
    font-size: 25px;
    line-height: 34px;
    text-transform: uppercase;
    color: #fff;
    width: 100%;
    padding: 12px 0;
    max-height: 59px;
}
#mobile nav {
    background-color: #1d1d1d;
}
#mobile nav .list-menu {
    display: flex;
    justify-content: center;
    align-items: center;
}
#mobile nav .list-menu .list-item {
    flex: 1;
    text-align: center;
    padding: 9px 0;
}
#mobile nav .list-menu .list-item.preorder {
    flex: 1;
}
#mobile nav .list-menu .list-item.preorder p {
    margin-bottom: 0;
    color: #fff;
    cursor: pointer;
    padding: 9px 0;
    background: #f00;
    border-radius: 3px;
    font-weight: bold;
    font-size: 16px;
    line-height: 116.68%;
}
#mobile .bang-gia .title {
    padding: 23px 0;
}
#mobile .bang-gia .title h4 {
    font-weight: bold;
    font-size: 24px;
    line-height: 116.68%;
    text-align: center;
    color: #000;
    margin-bottom: 0;
}
#mobile .bang-gia .slider {
    background: #f7f7f7;
    padding: 23px 0 25px;
}
#mobile .bang-gia .slider .prices {
    margin-bottom: 0;
}
#mobile .bang-gia .slider .prices .item {
    padding: 15px 9px;
}
#mobile .bang-gia .slider .prices .item .box {
    background: #fff;
    border-radius: 8px;
    padding: 11px;
}
#mobile .bang-gia .slider .prices .item .box:hover {
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
}
#mobile .bang-gia .slider .prices .item .box .btn-scroll-to {
    width: 100%;
    padding: 10px 0;
    margin-top: 11px;
    background: #1d1d1d;
    border-radius: 5px;
    font-weight: bold;
    font-size: 11px;
    line-height: 116.68%;
    color: #fff;
}
#mobile .bang-gia .slider .prices .item .box .btn-scroll-to:hover {
    background: #fff;
    border: 1px solid #1d1d1d;
    color: #1d1d1d;
}
#mobile .cau-hinh .nav-slider {
    margin-top: 35px;
    margin-bottom: 35px;
}
#mobile .cau-hinh .nav-slider .item .box {
    margin: 2px 4px;
    padding: 21px 0;
    background: #fff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    max-width: 204px;
    cursor: pointer;
}
#mobile .cau-hinh .nav-slider .item .box p {
    font-weight: 600;
    font-size: 19px;
    line-height: 26px;
    text-align: center;
    color: #000;
    margin-bottom: 0;
}
#mobile .cau-hinh .nav-slider .item.slick-current .box {
    background: #0075ff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}
#mobile .cau-hinh .nav-slider .item.slick-current .box p {
    color: #fff;
}
#mobile .cau-hinh .nav-slider .slick-list {
    padding: 0 !important;
}
#mobile .cau-hinh .nav-slider .slick-list .slick-track {
    width: 100% !important;
    transform: unset !important;
    display: flex;
    flex-wrap: wrap;
}
#mobile .cau-hinh .nav-slider .slick-list .slick-track .item {
    flex: 0 0 33.33%;
    margin-bottom: 10px;
    padding: 0 5px;
}
#mobile .cau-hinh .nav-slider .slick-list .slick-track .item .box {
    padding: 13px 0;
}
#mobile .cau-hinh .nav-slider .slick-list .slick-track .item .box p {
    font-size: 12px;
    line-height: 16px;
}
#mobile .cau-hinh .content-slider {
    margin-bottom: 32px;
}
#mobile .preorder-section {
    margin-bottom: 32px;
}
#mobile .preorder-section .box {
    background: #f8f8f8;
    border-radius: 10px;
    padding: 36px 5px 39px;
}
#mobile .preorder-section .box .product-img {
    margin-bottom: 10px;
}
#mobile .preorder-section .box .product-img .img {
    text-align: center;
}
#mobile .preorder-section .box .product-img .img-fluid {
    max-width: 300px;
}
#mobile .preorder-section .box .product-color {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 27px;
}
#mobile .preorder-section .box .product-color > p {
    font-size: 14px;
    line-height: 1;
    text-transform: capitalize;
    color: #0075ff;
    margin-right: 27px;
    margin-bottom: 0;
}
#mobile .preorder-section .box .product-color .colors {
    display: flex;
    flex-direction: row;
    align-items: center;
}
#mobile .preorder-section .box .product-color .colors .slt-color {
    margin-right: 13px;
    padding: 3px;
}
#mobile .preorder-section .box .product-color .colors .slt-color span {
    width: 18px;
    height: 18px;
}
#mobile .preorder-section .box .product-color .colors .slt-color.selected {
    border: 1px solid #000;
}
#mobile .preorder-section .box .product-color .colors .slt-color:last-child {
    margin-right: 0;
}
#mobile .preorder-section .box .product-picked > p {
    font-weight: 600;
    font-size: 18px;
    line-height: 25px;
    text-transform: capitalize;
    color: #000;
    margin-bottom: 18px;
}
#mobile .preorder-section .box .product-picked .picked .picked-box-wrapper {
    display: flex;
    align-items: stretch;
    margin-bottom: 10px;
}
#mobile .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box {
    border: 1px solid #0075ff;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: #fff;
    flex: 1;
    max-width: 137px;
    margin-right: 8px;
    padding: 4px 0;
}
#mobile .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box.type {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    font-size: 10px;
    line-height: 14px;
    text-align: center;
    color: #000;
}
#mobile .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box.option p {
    margin-bottom: 0;
}
#mobile .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box.option .capacity {
    font-weight: 600;
    font-size: 10px;
    line-height: 14px;
    text-align: center;
    text-transform: uppercase;
    color: #000;
}
#mobile .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box.option .price {
    font-weight: 600;
    font-size: 10px;
    line-height: 14px;
    text-align: center;
    text-transform: lowercase;
    color: #0075ff;
}
#mobile .preorder-section .box .product-picked .picked .picked-box-wrapper .picked-box.color {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    font-size: 10px;
    line-height: 14px;
    text-align: center;
    text-transform: capitalize;
    color: #0075ff;
}
#mobile .preorder-section .box .product-picked .picked .picked-box-wrapper .delete {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 10px;
    line-height: 14px;
    text-align: center;
    text-decoration-line: underline;
    text-transform: capitalize;
    color: #c40000;
    cursor: pointer;
}
#mobile .preorder-section .box .product-picked .picked .picked-box-wrapper .delete img {
    margin-right: 7px;
}
#mobile .preorder-section .box .product-title {
    font-weight: bold;
    font-size: 30px;
    line-height: 41px;
    color: #000;
    margin-bottom: 0;
    text-align: center;
}
#mobile .preorder-section .box .product-type {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 18px;
}
#mobile .preorder-section .box .product-type .type {
    flex: 0 0 calc(50% - 12px);
    background: #fff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 14px 0;
    cursor: pointer;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    color: #000;
    margin-left: 6px;
    margin-right: 6px;
    margin-bottom: 10px;
}
#mobile .preorder-section .box .product-type .type.selected {
    color: #fff;
    background: #0075ff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
}
#mobile .preorder-section .box .product-type .type:hover {
    box-shadow: 0px 0px 15px 5px rgba(0, 0, 0, 0.15);
}
#mobile .preorder-section .box .product-options {
    display: flex;
    flex-wrap: wrap;
}
#mobile .preorder-section .box .product-options .option {
    flex: 0 0 calc(50% - 12px);
    cursor: pointer;
    background: #fff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 4px 0;
    margin-left: 6px;
    margin-right: 6px;
    margin-bottom: 10px;
}
#mobile .preorder-section .box .product-options .option .capacity {
    font-size: 17px;
    line-height: 23px;
    text-align: center;
    text-transform: uppercase;
    color: #000;
    margin-bottom: 0;
}
#mobile .preorder-section .box .product-options .option .price {
    font-size: 14px;
    line-height: 19px;
    text-align: center;
    text-decoration-line: line-through;
    text-transform: lowercase;
    color: #a8a8a8;
    margin-bottom: 0;
}
#mobile .preorder-section .box .product-options .option .sale {
    font-weight: 600;
    font-size: 18px;
    line-height: 25px;
    text-align: center;
    text-transform: lowercase;
    color: #0075ff;
    margin-bottom: 0;
}
#mobile .preorder-section .box .product-options .option.selected {
    border: 1px solid #0075ff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
}
#mobile .preorder-section .box .product-options .option:hover {
    box-shadow: 0px 0px 15px 5px rgba(0, 0, 0, 0.15);
}
#mobile .preorder-section .box .khuyen-mai h4 {
    font-weight: 600;
    font-size: 18px;
    line-height: 25px;
    text-transform: capitalize;
    color: #c40000;
}
#mobile .preorder-section .box .khuyen-mai ol {
    padding-left: 1rem;
}
#mobile .preorder-section .box .khuyen-mai ol li::marker {
    font-size: 16px;
}
#mobile .preorder-section .box .khuyen-mai ol li p {
    font-size: 16px;
    line-height: 150.68%;
    color: #2e2e2e;
    margin-bottom: 0;
}
#mobile .preorder-section .box .khuyen-mai ol li p span {
    font-weight: 600;
    font-size: 16px;
    line-height: 150.68%;
    color: #c40000;
}
#mobile .preorder-section .box .khuyen-mai ol li .form-check label {
    font-size: 16px;
    line-height: 186.68%;
    color: #2e2e2e;
    margin-bottom: 0;
    cursor: pointer;
}
#mobile .preorder-section .box .khuyen-mai ol li .form-check label span {
    font-weight: 600;
    font-size: 16px;
    line-height: 186.68%;
    color: #c40000;
}
#mobile .preorder-section .box .khuyen-mai ol li .form-check .checkmark {
    border: 1px solid #000;
    width: 16px;
    height: 16px;
    border-radius: 50px;
    position: absolute;
    left: -5px;
    top: 7px;
}
#mobile .preorder-section .box .khuyen-mai ol li .form-check input:checked ~ .checkmark {
    background: #c40000;
}
#mobile .preorder-section .box .khuyen-mai ol li .form-check input:checked ~ .checkmark::after {
    content: "";
    position: absolute;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: solid 3px white;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
}
#mobile .preorder-section .box .btn-wrapper {
    margin-top: 32px;
    text-align: center;
}
#mobile .preorder-section .box .btn-wrapper .btn {
    background: #c40000;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    width: 100%;
    max-width: 500px;
    padding: 11px 0;
    font-weight: bold;
    font-size: 19px;
    line-height: 26px;
    text-align: center;
    text-transform: capitalize;
    color: #fff;
}
#mobile .preorder-section .box .btn-wrapper .btn:hover {
    opacity: 0.7;
}
#mobile .gifts {
    background-color: #252525;
    padding-top: 31px;
    padding-left: 0;
    padding-right: 0;
}
#mobile .gifts .title {
    margin-bottom: 48px;
}
#mobile .gifts .title h4 {
    font-weight: 600;
    font-size: 25px;
    line-height: 34px;
    text-align: center;
    letter-spacing: 0.01em;
    text-transform: capitalize;
    color: #fff;
    margin-bottom: 0;
}
#mobile .gifts .title h3 {
    font-weight: bold;
    font-size: 36px;
    line-height: 45px;
    text-align: center;
    letter-spacing: 0.01em;
    text-transform: capitalize;
    color: #fff;
    margin-bottom: 0;
}
#mobile .gifts .list-items .list-item {
    padding: 0 6px;
}
#mobile .gifts .list-items .list-item .box {
    background: #fff;
    border-radius: 8px;
    padding: 8px 12px;
}
#mobile .gifts .list-items .list-item .box > img {
    margin-bottom: 8px;
}
#mobile .gifts .list-items .list-item .box .capacity {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}
#mobile .gifts .list-items .list-item .box .capacity span:not(.number) {
    width: 20px;
    height: 20px;
    cursor: pointer;
}
#mobile .gifts .list-items .list-item .box .capacity span:not(.number) img {
    width: 100%;
}
#mobile .gifts .list-items .list-item .box .capacity span:not(.number):hover {
    opacity: 0.7;
}
#mobile .gifts .list-items .list-item .box .capacity span.number {
    font-weight: 600;
    font-size: 15px;
    line-height: 116.68%;
    color: #434343;
    margin-left: 14px;
    margin-right: 14px;
}
#mobile .gifts .list-items .list-item .color {
    justify-content: flex-start;
    padding-top: 18px;
    padding-bottom: 25px;
}
#mobile .gifts .list-items .list-item .color > span {
    font-weight: bold;
    font-size: 9px;
    text-transform: capitalize;
    color: #fff;
    margin-right: 11px;
}
#mobile .gifts .list-items .list-item .color .selector-color {
    display: flex;
    justify-content: center;
    align-items: center;
}
#mobile .gifts .list-items .list-item .color .selector-color label {
    margin-right: 6px;
    padding: 2px;
}
#mobile .gifts .list-items .list-item .color .selector-color label span {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}
#mobile .gifts .list-items .list-item .color .selector-color label.selected {
    border: 1px solid #fff;
}
#mobile .gifts .list-items .list-item .color .selector-color label:last-child {
    margin-right: 0;
}
#mobile .preorder-form .box {
    background: #efefef;
    border-radius: 10px;
    margin: 18px auto;
    padding: 33px 16px 35px;
}
#mobile .preorder-form .box .list > p {
    font-weight: bold;
    font-size: 25px;
    line-height: 34px;
    text-transform: capitalize;
    color: #c30000;
}
#mobile .preorder-form .box .list .list-product .product {
    margin-bottom: 10px;
}
#mobile .preorder-form .box .list .list-product .product .img-product {
    padding-right: 0;
}
#mobile .preorder-form .box .list .list-product .product .info-product .type {
    font-weight: bold;
    font-size: 15px;
    line-height: 20px;
    text-transform: capitalize;
    color: #000;
    margin-bottom: 0;
}
#mobile .preorder-form .box .list .list-product .product .info-product .option, #mobile .preorder-form .box .list .list-product .product .info-product .quantity {
    font-weight: 600;
    font-size: 15px;
    line-height: 20px;
    text-transform: capitalize;
    color: #000;
    margin-bottom: 0;
}
#mobile .preorder-form .box .list .list-product .product .info-product .option span, #mobile .preorder-form .box .list .list-product .product .info-product .quantity span {
    font-weight: normal;
    font-size: 15px;
    line-height: 20px;
    text-transform: capitalize;
    color: #000;
}
#mobile .preorder-form .box .form p {
    font-weight: bold;
    font-size: 18px;
    line-height: 25px;
    color: #303030;
    margin-bottom: 0;
}
#mobile .preorder-form .box .form input {
    background: #fff;
    border-radius: 10px;
    font-size: 12px;
    line-height: 16px;
    color: #808080;
    margin-bottom: 15px;
}
#mobile .preorder-form .box .form span.note {
    font-size: 8px;
    line-height: 9px;
    color: #ff2727;
}
#mobile .preorder-form .box .form .btn {
    margin-top: 6px;
    background: #c40000;
    border-radius: 12px;
    font-weight: bold;
    font-size: 15px;
    line-height: 20px;
    text-transform: uppercase;
    color: #fff;
    width: 100%;
    padding: 10px 0;
    max-height: 59px;
}
@media screen and (min-width: 768px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl {
        max-width: 1138px;
    }
}
@media screen and (min-width: 992px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl {
        max-width: 1362px;
    }
}
@media screen and (min-width: 1200px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl {
        max-width: 1570px;
    }
}
@media screen and (max-width: 1199px) {
    #desktop .gifts .list-items .list-item {
        flex: 0 0 33%;
    }
}
