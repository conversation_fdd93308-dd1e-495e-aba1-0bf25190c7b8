<?php $ini = parse_ini_file('app.ini'); ?>
<!DOCTYPE html>
<html lang="vi">

<head>
    <title>Quản Trị - <?= $ini['app_title'] ?></title>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=0" />
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico" />
    <link rel="stylesheet" href="css/bootstrap.min.css" />
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
            color: #588c7e;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
        }

        thead th {
            background-color: #588c7e;
            color: white;
            text-transform: uppercase;
        }

        .none-preorder {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>

<body>
    <div class="table-responsive">
        <?php
        try {
            $con = new PDO('mysql:host=' . $ini['db_host'] . ';dbname=' . $ini['db_name'], $ini['db_user'], $ini['db_password']);
            $con->exec("set names utf8");
            $con->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $query = "SELECT * FROM members";
            //first pass just gets the column names
            $result = $con->query($query);
            //return only the first row (we only need field names)
            $row = $result->fetch(PDO::FETCH_ASSOC);
            if (!empty($row)) {
                echo "<table class='table table-hover'>";
                echo "<thead><tr>";
                foreach ($row as $field => $value) {
                    echo " <th scope='col'>$field</th> ";
                } // end foreach
                //second query gets the data
                $data = $con->query($query);
                $data->setFetchMode(PDO::FETCH_ASSOC);
                echo " </tr></thead><tbody>";
                foreach ($data as $row) {
                    echo " <tr> ";
                    foreach ($row as $name => $value) {
                        if ($name == 'id') {
                            echo " <th scope='row'>$value</th> ";
                        } elseif ($name == 'product') {
                            $products = json_decode($value);
                            $productsHtml = "<th scope='row'>";
                            if ($products != null) {
                                foreach ($products as $product) {
                                    $productsHtml .= '<p>' . $product->type;
                                    if (!empty($product->capacity)) {
                                        $productsHtml .= ' - ' . $product->capacity;
                                    }

                                    if (!empty($product->color)) {
                                        $productsHtml .= ' - ' . $product->color;
                                    }

                                    if (!empty($product->quantity)) {
                                        $productsHtml .= ' - Số lượng: ' . $product->quantity;
                                    }

                                    if (!empty($product->km)) {
                                        $productsHtml .= ' - Khuyến mãi: ' . $product->km;
                                    }
                                    $productsHtml .= '</p>';
                                }
                            } else {
                                $productsHtml .= $value;
                            }
                            $productsHtml .= "</th>";
                            echo $productsHtml;
                        } else {
                            echo " <td>$value</td> ";
                        }
                    } // end field loop
                    echo " </tr> ";
                } // end record loop
                echo "</tbody></table>";
            } else {
                echo "<h4 class='none-preorder'>Chưa có đơn đặt hàng nào.</h4>";
            }
        } catch (PDOException $e) {
            echo 'ERROR: ' . $e->getMessage();
        } // end try
        ?>
    </div>
</body>

<script src="//code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="js/bootstrap.bundle.min.js"></script>

</html>